/**
 * Main Entry Point
 * Initializes the dependency visualizer application
 * Enhanced with crash-resistant folder loading
 */

import DependencyVisualizerApp from './core/app.js';
import EnhancedFileProcessor from './utils/enhanced-file-processor.js';

// Constants
const THEME_KEY = 'dependency-visualizer-theme';
const DEFAULT_EXCLUDE_RULES = { folders: ["node_modules", ".git", "dist", "build", "__pycache__", "venv", "target", "out", "bin"] };
const WORKER_THRESHOLD = 50;
const SVG_NS = "http://www.w3.org/2000/svg";

const MOON_ICON = `<svg viewBox="0 0 24 24" fill="currentColor" width="1em" height="1em"><path d="M10 2c-1.82 0-3.53.5-5 1.35C7.99 5.08 10 8.3 10 12s-2.01 6.92-5 8.65C6.47 21.5 8.18 22 10 22c5.52 0 10-4.48 10-10S15.52 2 10 2z"></path></svg>`;
const SUN_ICON = `<svg viewBox="0 0 24 24" fill="currentColor" width="1em" height="1em"><path d="M12 3a1 1 0 00-1 1v2a1 1 0 102 0V4a1 1 0 00-1-1zm0 14a1 1 0 00-1 1v2a1 1 0 102 0v-2a1 1 0 00-1-1zm5.657-12.657a1 1 0 00-1.414 0l-1.414 1.414a1 1 0 101.414 1.414l1.414-1.414a1 1 0 000-1.414zM6.343 17.657a1 1 0 00-1.414 0l-1.414 1.414a1 1 0 101.414 1.414l1.414-1.414a1 1 0 000-1.414zm12.657 0a1 1 0 000 1.414l1.414 1.414a1 1 0 001.414-1.414l-1.414-1.414a1 1 0 00-1.414 0zM7.757 6.343a1 1 0 000 1.414l1.414 1.414a1 1 0 001.414-1.414L9.172 6.343a1 1 0 00-1.414 0zM4 12a1 1 0 00-1-1H1a1 1 0 100 2h2a1 1 0 001-1zm14 0a1 1 0 00-1-1h-2a1 1 0 100 2h2a1 1 0 001-1zM12 7a5 5 0 100 10 5 5 0 000-10z"></path></svg>`;

// DOM element references
// These are initialized early, but their existence is critical for app functionality.
// Checks for their existence will be done in DOMContentLoaded.
const DOM = {
    projectFolderInput: document.getElementById('project-folder-input'),
    progressBarContainer: document.getElementById('progress-bar-container'),
    progressBar: document.getElementById('progress-bar'),
    status: document.getElementById('status'),
    fileDetailsPanel: document.getElementById('file-details-panel'),
    closeDetailsPanelBtn: document.getElementById('close-details-panel'),
    codePreviewModal: document.getElementById('code-preview-modal'),
    themeToggleButton: document.getElementById('theme-toggle'),
    themeIconContainer: document.getElementById('theme-icon-container'),
    visualization: document.getElementById('visualization-container'), // For custom SVG viz
    appContainer: document.getElementById('visualization-container'), // For DependencyVisualizerApp
    panelContainer: document.getElementById('details-panel-container'),
    nodeInfoContent: document.getElementById('node-info-content'),
};

// Global/Module-scoped variables
let isLightTheme = localStorage.getItem(THEME_KEY) === 'light';
let workerResults = [];
let totalFilesProcessed = 0;
let totalFilesExpected = 0;
let excludedFolders = [...DEFAULT_EXCLUDE_RULES.folders];
let excludedFiles = [];
let activeWorkers = [];
let workerLastActive = {};
let modalStylesAdded = false;

// Enhanced file processor for crash-resistant processing
let enhancedFileProcessor = null;

// Helper function to escape HTML special characters
function escapeHtml(text) {
    if (typeof text !== 'string') {
        if (text === null || typeof text === 'undefined') return '';
        text = String(text);
    }
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Helper function to immediately clean up file references from memory with enhanced cleanup
function cleanupFileReferences(filesList, context = 'Unknown') {
    if (!filesList || filesList.length === 0) return 0;



    // Enhanced memory cleanup for file references
    for (let i = 0; i < filesList.length; i++) {
        const file = filesList[i];
        if (file) {
            // Clear all possible file content and references
            if (file.stream) {
                try {
                    // Cancel any active streams
                    if (file.stream.cancel) file.stream.cancel();
                    if (file.stream.abort) file.stream.abort();
                } catch (e) {
                    // Ignore stream cancellation errors
                }
                file.stream = null;
            }

            // Clear file content references
            if (file.arrayBuffer) file.arrayBuffer = null;
            if (file.text) file.text = null;
            if (file.slice) file.slice = null;

            // Clear any cached content
            if (file._content) file._content = null;
            if (file._text) file._text = null;
            if (file._arrayBuffer) file._arrayBuffer = null;

            // Note: lastModified and lastModifiedDate are read-only getters, so we can't clear them
            // But we can clear any custom properties that might exist

            // Clear any event listeners
            if (file.removeEventListener) {
                try {
                    file.removeEventListener('load', null);
                    file.removeEventListener('error', null);
                } catch (e) {
                    // Ignore event listener removal errors
                }
            }

            // Nullify the file reference
            filesList[i] = null;
        }
    }

    const cleanedCount = filesList.length;

    // Clear the array completely using multiple methods
    filesList.length = 0;
    filesList.splice(0);

    // Additional cleanup for array references
    if (filesList.constructor === Array) {
        while (filesList.length > 0) {
            filesList.pop();
        }
    }

    // Force immediate garbage collection if available
    if (window.gc) {
        setTimeout(() => {
            window.gc();

        }, 50); // Reduced timeout for faster cleanup
    }

    // Additional memory pressure relief
    if (window.performance && window.performance.memory) {
        const memoryBefore = window.performance.memory.usedJSHeapSize;
        setTimeout(() => {
            const memoryAfter = window.performance.memory.usedJSHeapSize;
            const memoryFreed = memoryBefore - memoryAfter;
            if (memoryFreed > 0) {

            }
        }, 200);
    }


    return cleanedCount;
}

// Global function to show code preview modal
window.showCodePreview = function(filePath, lineNumber, itemName, codeSnippetForDisplay, snippetOriginalStartLine) {

    
    const modal = DOM.codePreviewModal; // Already initialized
    const modalTitle = modal ? modal.querySelector('#code-preview-title') : null;
    const modalContent = modal ? modal.querySelector('#code-preview-content') : null;

    if (!modal || !modalTitle || !modalContent) {
        console.error('Code preview modal elements not found. Ensure modal with ID "code-preview-modal" and children with IDs "code-preview-title" and "code-preview-content" exist.');
        return;
    }
    
    modalTitle.textContent = `Preview: ${escapeHtml(itemName || 'Code')} (line ${lineNumber} in ${filePath ? escapeHtml(filePath.split('/').pop()) : 'Unknown File'})`;
    
    if (codeSnippetForDisplay) {
        window.displayCodeContent(codeSnippetForDisplay, snippetOriginalStartLine || 1, lineNumber, modalContent);
        modal.style.display = 'block';
        return;
    }
    
    if (window.dependencyVisualizerApp && typeof window.dependencyVisualizerApp.getCodeSnippet === 'function') {
        window.dependencyVisualizerApp.getCodeSnippet(filePath, lineNumber)
            .then(snippetData => {
                if (snippetData && typeof snippetData.snippet === 'string') {
                    const actualStartLine = snippetData.startLine || Math.max(1, lineNumber - 5);
                    window.displayCodeContent(snippetData.snippet, actualStartLine, lineNumber, modalContent);
                } else {
                    modalContent.innerHTML = `<div class="error-message">Code snippet not found for ${escapeHtml(filePath)} at line ${lineNumber}.</div>`;
                }
                modal.style.display = 'block';
            })
            .catch(error => {
                console.error('Error fetching code snippet:', error);
                modalContent.innerHTML = `<div class="error-message">Error loading code preview: ${escapeHtml(error.message)}</div>`;
                modal.style.display = 'block';
            });
    } else {
        modalContent.innerHTML = '<div class="error-message">Code preview not available (App component not ready or method missing).</div>';
        modal.style.display = 'block';
    }
};

// Helper function to display code content with line highlighting in the modal
window.displayCodeContent = function(codeSnippet, snippetOriginalStartLine, targetOriginalLineNumber, modalContentEl) {
    const lines = String(codeSnippet || '').split('\n');
    let html = '<pre class="code-preview-pre"><code>';

    const highlightIndexInSnippet = targetOriginalLineNumber - snippetOriginalStartLine;

    for (let i = 0; i < lines.length; i++) {
        const currentOriginalLineNum = snippetOriginalStartLine + i;
        const lineClass = i === highlightIndexInSnippet ? 'highlighted-line' : '';

        html += `<div class="${lineClass}" data-line="${currentOriginalLineNum}">`;
        html += `<span class="line-number">${currentOriginalLineNum}</span>${escapeHtml(lines[i] || '')}</div>`;
    }
    html += '</code></pre>';

    if (highlightIndexInSnippet >= 0 && highlightIndexInSnippet < lines.length) {
        const highlightedCode = lines[highlightIndexInSnippet] || '';
        html += `<div class="code-actions">`;
        html += `<button class="copy-btn" data-clipboard-text="${escapeHtml(highlightedCode)}">Copy Line ${targetOriginalLineNumber}</button>`;
        html += `</div>`;
    }
    modalContentEl.innerHTML = html;

    const copyButton = modalContentEl.querySelector('.copy-btn');
    if (copyButton) {
        copyButton.addEventListener('click', function() {
            if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
                navigator.clipboard.writeText(this.dataset.clipboardText)
                .then(() => {
                    const originalText = this.textContent;
                    this.textContent = 'Copied!';
                    setTimeout(() => { this.textContent = originalText; }, 1500);
                })
                .catch(err => {
                    console.error('Failed to copy text using navigator.clipboard: ', err);
                    alert('Failed to copy text. See console for details.');
                });
            } else {
                try {
                    const textArea = document.createElement("textarea");
                    textArea.value = this.dataset.clipboardText;
                    textArea.style.position = "fixed";
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    const originalText = this.textContent;
                    this.textContent = 'Copied (fallback)!';
                    setTimeout(() => { this.textContent = originalText; }, 1500);
                } catch (err) {
                     console.error('Fallback copy failed: ', err);
                     alert('Failed to copy text.');
                }
            }
        });
    }
};

// Global function to handle direct clicks on code items
window.handleCodePreviewClick = function(e) {
    const targetElement = e.currentTarget || e.target;
    
    let itemName = targetElement.getAttribute('data-name');
    let lineNumberStr = targetElement.getAttribute('data-line');
    let filePath = targetElement.getAttribute('data-file-path');

    if (targetElement.closest('.events-list')) {
        const eventDataAttr = targetElement.getAttribute('data-event');
        if (eventDataAttr) {
            try {
                const parsedEvent = JSON.parse(eventDataAttr);
                if (parsedEvent) {
                    filePath = parsedEvent.filePath || filePath;
                    lineNumberStr = parsedEvent.line ? String(parsedEvent.line) : lineNumberStr;
                    itemName = parsedEvent.name || itemName;
                }
            } catch (err) {
                console.error('Failed to parse data-event attribute:', err, eventDataAttr);
            }
        }
    }
    
    if (!filePath) {
        let current = targetElement;
        while (current && current !== document.body) {
            filePath = current.getAttribute('data-file-path');
            if (filePath) break;
            current = current.parentElement;
        }
    }
    
    const panelContainerEl = document.getElementById('details-panel-container'); // Re-fetch in case DOM object wasn't ready
    if (!filePath && panelContainerEl) {
        filePath = panelContainerEl.getAttribute('data-current-file');
    }

    if (!filePath && window.dependencyVisualizerApp && typeof window.dependencyVisualizerApp.getCurrentSelectedNodePath === 'function') {
        filePath = window.dependencyVisualizerApp.getCurrentSelectedNodePath();
    }

    if (!itemName || !lineNumberStr) {
        const text = targetElement.textContent || "";
        const matches = text.match(/(.+?)\s*\(line\s*(\d+)\)/i) || text.match(/(.+?)\s*Line:\s*(\d+)/i);
        if (matches && matches.length > 2) {
            itemName = itemName || matches[1].trim();
            lineNumberStr = lineNumberStr || matches[2];
        }
    }

    const lineNumber = parseInt(lineNumberStr);

    if (!itemName || isNaN(lineNumber) || lineNumber <= 0) {
        console.error('Could not extract valid item name or line number. ItemName:', itemName, 'LineStr:', lineNumberStr, 'Element:', targetElement);
        return;
    }

    if (!filePath) {
        console.error('No file path found for code preview. Element:', targetElement.outerHTML);
        alert('Could not determine the file path for this code item. Preview unavailable.');
        return;
    }

    showCodePreview(filePath, lineNumber, itemName);
    
    if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
    }
};

// Creates HTML for a section in the details panel
const createSectionHTML = (title, items, className, formatter) => {
    if (!items || !Array.isArray(items) || items.length === 0 || typeof formatter !== 'function') {
        return '';
    }
    const isEventsSection = className.includes('events-title') || title.toLowerCase().includes('event');
    return `
    <div class="details-section ${isEventsSection ? 'events-list' : ''}">
        <h3 class="section-title ${className}">${escapeHtml(title)} (${items.length})</h3>
        <ul>${items.map(item => {
            const itemName = (typeof item === 'string' ? item : (item.name || 'unknown')).trim();
            const itemLine = item.line || '';
            let dataAttrs = `data-line="${escapeHtml(itemLine)}" data-name="${escapeHtml(itemName)}"`;
            if (isEventsSection && typeof item === 'object' && item.filePath) {
                dataAttrs += ` data-file-path="${escapeHtml(item.filePath)}"`;
            }
            if (isEventsSection && typeof item === 'object') {
                try {
                    const eventData = escapeHtml(JSON.stringify(item));
                    dataAttrs += ` data-event="${eventData}"`;
                } catch (e) {
                    console.error("Error stringifying event data for data attribute:", item, e);
                }
            }
            return `<li class="code-item${isEventsSection ? ' event-item' : ''}" ${dataAttrs} onclick="window.handleCodePreviewClick(event)">${formatter(item)}</li>`;
        }).join('')}</ul>
    </div>`;
};

// Loads exclusion rules
const loadExcludeRules = () => {
    return fetch('exclude.json')
        .then(response => {
            if (!response.ok) {
                console.warn(`Failed to load exclude.json (status: ${response.status}). Using default exclusion rules.`);
                return DEFAULT_EXCLUDE_RULES;
            }
            return response.json();
        })
        .then(data => {
            if (data && Array.isArray(data.folders)) {
                excludedFolders = [...data.folders];
                excludedFiles = Array.isArray(data.files) ? [...data.files] : [];
                return data;
            } else {
                console.warn("exclude.json is malformed or missing 'folders' array. Using default rules.");
                excludedFolders = [...DEFAULT_EXCLUDE_RULES.folders];
                excludedFiles = [];
                return DEFAULT_EXCLUDE_RULES;
            }
        })
        .catch(error => {
            console.error('Error fetching or parsing exclude.json. Using default exclusion rules.', error);
            excludedFolders = [...DEFAULT_EXCLUDE_RULES.folders];
            excludedFiles = [];
            return DEFAULT_EXCLUDE_RULES;
        });
};

// Search filter for details panel
const handleSearchFilter = e => {
    const detailsPanelEl = document.getElementById('file-details-panel'); // Re-fetch
    if (!detailsPanelEl || e.target.id !== 'details-search') return;
    const searchText = e.target.value.toLowerCase().trim();
    
    detailsPanelEl.querySelectorAll('.details-section').forEach(section => {
        const titleElement = section.querySelector('h3.section-title');
        const listItems = section.querySelectorAll('ul > li');
        let sectionHasVisibleItem = false;
        if (titleElement && titleElement.textContent.toLowerCase().includes(searchText)) {
            sectionHasVisibleItem = true;
        }
        listItems.forEach(item => {
            if (item.textContent.toLowerCase().includes(searchText)) {
                item.style.display = '';
                sectionHasVisibleItem = true;
            } else {
                item.style.display = 'none';
            }
        });
        if (titleElement) titleElement.style.display = sectionHasVisibleItem ? '' : 'none';
        const ul = section.querySelector('ul');
        if (ul) ul.style.display = sectionHasVisibleItem ? '' : 'none';
        section.style.display = sectionHasVisibleItem ? '' : 'none';
    });
};

// Populates left panel with scan summary
const populateLeftPanelWithScanResults = (scanResults) => {
    const scanSummaryEl = document.getElementById('scan-summary-content'); // Re-fetch
    if (!scanResults || !scanSummaryEl) {
        console.warn("populateLeftPanelWithScanResults: No scan results or panel element.");
        if (scanSummaryEl) scanSummaryEl.innerHTML = "<p>No scan results to display.</p>";
        return;
    }

    const totalFiles = Object.keys(scanResults.files || {}).length;
    // ... (rest of the counts) ...
    let totalFunctions = 0, totalVariables = 0, totalImports = 0, totalExports = 0, totalEvents = 0;
    Object.values(scanResults.files || {}).forEach(file => {
        totalFunctions += (file.functions || []).length;
        totalVariables += (file.variables || []).length;
        totalImports += (file.imports || []).length;
        totalExports += (file.exports || []).length;
        totalEvents += (file.events || []).length;
    });


    // Calculate connection statistics
    const edges = scanResults.graph?.edges || [];
    const totalConnections = edges.length;
    const workerConnections = edges.filter(edge => edge.type === 'worker').length;
    const eventConnections = edges.filter(edge => edge.type === 'event').length;
    const importConnections = edges.filter(edge => edge.type === 'import').length;
    const exportConnections = edges.filter(edge => edge.type === 'export').length;

    const scanSummaryHTML = `
    <div class="details-section">
        <h3 class="section-title scan-summary-title">📊 Scan Results Summary</h3>
        <ul>
            <li><strong>Total Files Scanned:</strong> ${totalFiles}</li>
            <li><strong>Graph Nodes:</strong> ${scanResults.graph?.nodes?.length || 0}</li>
            <li><strong>Total Connections:</strong> ${totalConnections}</li>
            <li><strong>Import Connections:</strong> ${importConnections}</li>
            <li><strong>Export Connections:</strong> ${exportConnections}</li>
            <li><strong>Worker Connections:</strong> ${workerConnections}</li>
            <li><strong>Event Connections:</strong> ${eventConnections}</li>
            <li><strong>Functions/Classes:</strong> ${totalFunctions}</li>
            <li><strong>Variables:</strong> ${totalVariables}</li>
            <li><strong>Imports:</strong> ${totalImports}</li>
            <li><strong>Exports:</strong> ${totalExports}</li>
            <li><strong>Events:</strong> ${totalEvents}</li>
            <li><strong>Last Updated:</strong> ${new Date(scanResults.lastUpdated || Date.now()).toLocaleString()}</li>
        </ul>
    </div>`;

    const fileComplexity = Object.entries(scanResults.files || {}).map(([path, file]) => ({
        path,
        complexity: (file.functions?.length || 0) + (file.variables?.length || 0),
        functions: file.functions?.length || 0,
        variables: file.variables?.length || 0,
        imports: file.imports?.length || 0,
        exports: file.exports?.length || 0
    })).sort((a, b) => b.complexity - a.complexity).slice(0, 10);

    const topFilesHTML = fileComplexity.length > 0 ? `
    <div class="details-section">
        <h3 class="section-title top-files-title">🏆 Most Complex Files</h3>
        <ul>
        ${fileComplexity.map(file => `
            <li class="file-item" data-file-path="${escapeHtml(file.path)}" title="Click to view details for ${escapeHtml(file.path.split('/').pop())}" style="margin-bottom: 12px; padding: 8px; border: 1px solid var(--border-color, rgba(255,255,255,0.2)); border-radius: 6px; cursor:pointer;">
                <div class="file-header" style="font-weight: bold; margin-bottom: 6px;">
                    📄 ${escapeHtml(file.path.split('/').pop())}
                </div>
                <div class="file-properties" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 4px; font-size: 0.9em;">
                    ${file.functions > 0 ? `<div class="property-item" data-file-path="${escapeHtml(file.path)}" data-property="functions" title="View functions" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(100,149,237,0.2);">⚙️ Functions: ${file.functions}</div>` : ''}
                    ${file.variables > 0 ? `<div class="property-item" data-file-path="${escapeHtml(file.path)}" data-property="variables" title="View variables" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(255,165,0,0.2);">📊 Variables: ${file.variables}</div>` : ''}
                    ${file.imports > 0 ? `<div class="property-item" data-file-path="${escapeHtml(file.path)}" data-property="imports" title="View imports" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(50,205,50,0.2);">📥 Imports: ${file.imports}</div>` : ''}
                    ${file.exports > 0 ? `<div class="property-item" data-file-path="${escapeHtml(file.path)}" data-property="exports" title="View exports" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(255,69,0,0.2);">📤 Exports: ${file.exports}</div>` : ''}
                </div>
            </li>`).join('')}
        </ul>
    </div>` : '';

    const searchHTML = `
    <div class="search-container" style="padding: 8px; margin-bottom: 8px; position: sticky; top: 0; background: var(--panel-bg, #333); z-index: 10;">
        <input type="text" id="details-search" placeholder="Search summary..." style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid var(--border-color, #555); background: var(--input-bg, #444); color: var(--text-color, #fff);" />
    </div>`;
    const instructionsHTML = `
    <div class="details-section">
        <h3 class="section-title instructions-title">ℹ️ Instructions</h3>
        <p>Scan complete! Click on a node or a file/property above. Use search to filter summary.</p>
    </div>`;

    scanSummaryEl.innerHTML = searchHTML + scanSummaryHTML + topFilesHTML + instructionsHTML;
    scanSummaryEl.setAttribute('data-scan-context', 'summary');

    // Show the scan summary panel and activate the nav item
    const scanSummaryPanel = document.getElementById('scan-summary-panel');
    const scanSummaryNavItem = document.getElementById('nav-scan-summary');
    const contentArea = document.querySelector('.content-scroll-area');

    if (scanSummaryPanel && scanSummaryNavItem) {
        // Remove active from all nav items
        document.querySelectorAll('.sidebar-item').forEach(item => item.classList.remove('active'));
        // Activate scan summary nav item
        scanSummaryNavItem.classList.add('active');
        // Show the panel
        scanSummaryPanel.style.display = 'flex';
        contentArea?.classList.add('scan-summary-active');
    }

    scanSummaryEl.querySelectorAll('.file-item').forEach(item => {
        item.addEventListener('click', (e) => {
            if (e.target.closest('.property-item')) return;
            const filePath = item.dataset.filePath;
            if (filePath && window.dependencyVisualizerApp?.selectNodeAndOpenFileDetails) {
                 window.dependencyVisualizerApp.selectNodeAndOpenFileDetails(filePath);
            } else if (filePath && scanResults.files[filePath]) {
                const nodeData = { id: filePath, name: filePath.split('/').pop(), path: filePath, type: 'file', ...scanResults.files[filePath] };
                openDetailsPanel(nodeData);
            }
        });
    });
    scanSummaryEl.querySelectorAll('.property-item').forEach(item => {
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            const filePath = item.dataset.filePath;
            const property = item.dataset.property;
            if (filePath && window.dependencyVisualizerApp?.selectNodeAndOpenFileDetails) {
                 window.dependencyVisualizerApp.selectNodeAndOpenFileDetails(filePath, property);
            } else if (filePath && scanResults.files[filePath]) {
                const nodeData = { id: filePath, name: filePath.split('/').pop(), path: filePath, type: 'file', ...scanResults.files[filePath] };
                openDetailsPanel(nodeData, property);
            }
        });
    });

    const searchInput = scanSummaryEl.querySelector('#details-search');
    if (searchInput) searchInput.addEventListener('input', handleSearchFilter);

    scanSummaryEl.querySelectorAll('.section-title').forEach(header => {
        header.addEventListener('click', () => {
            header.classList.toggle('collapsed');
            const content = header.nextElementSibling;
            if (content) content.classList.toggle('collapsed');
        });
        if (!header.classList.contains('scan-summary-title') && !header.classList.contains('instructions-title')) {
             header.classList.add('collapsed');
             const content = header.nextElementSibling;
             if (content) content.classList.add('collapsed');
        }
    });
};

// Populates right panel with specific node details
window.openDetailsPanel = (nodeData, focusProperty = null) => {
    const detailsPanelEl = document.getElementById('file-details-panel'); // Re-fetch
    const panelContainerEl = document.getElementById('details-panel-container'); // Re-fetch

    if (!detailsPanelEl) {
        console.error("Details panel element ('#file-details-panel') not found.");
        return;
    }

    if (!nodeData || !nodeData.id) {
        detailsPanelEl.innerHTML = "<div class='details-section'><h3 class='section-title'>Ready</h3><p>Select a node or file.</p></div>";
        if (panelContainerEl) panelContainerEl.removeAttribute('data-current-file');
        return;
    }
    
    if (!modalStylesAdded) {
        const styles = `
        <style>
            .modal { display: none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.7); }
            .modal-content { background-color: var(--modal-bg, #2a2a2a); margin: 5% auto; padding: 20px; border: 1px solid var(--border-color, #444); width: 80%; max-width: 1000px; border-radius: 5px; box-shadow: 0 4px 15px rgba(0,0,0,0.5); color: var(--text-color, #fff); position: relative; }
            .modal-header { padding-bottom: 10px; border-bottom: 1px solid var(--border-color, #444); display: flex; justify-content: space-between; align-items: center; }
            .modal-header h3 { margin: 0; font-size: 1.2em; }
            .modal .close { color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer; padding: 0 5px; }
            .modal .close:hover, .modal .close:focus { color: #fff; text-decoration: none; }
            .modal-body { padding-top: 10px; }
            .code-preview-content { max-height: 70vh; overflow-y: auto; background-color: var(--code-bg, #1e1e1e); border-radius: 4px; padding: 10px; font-family: 'Consolas', 'Monaco', monospace; font-size: 0.9em; line-height: 1.5; }
            .code-preview-pre div { padding: 1px 0; }
            .code-preview-pre .line-number { display: inline-block; width: 3.5em; color: #858585; text-align: right; padding-right: 1em; user-select: none; }
            .code-preview-pre .highlighted-line { background-color: var(--highlight-bg, rgba(255, 255, 0, 0.15)); display: block; }
            .code-actions { margin-top: 10px; text-align: right; }
            .copy-btn { padding: 6px 12px; background-color: var(--button-bg, #007bff); color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.9em; }
            .copy-btn:hover { background-color: var(--button-hover-bg, #0056b3); }
            .error-message { color: #dc3545; padding: 10px; border: 1px solid #dc3545; background-color: rgba(220,53,69,0.1); border-radius: 3px; }
        </style>`;
        document.head.insertAdjacentHTML('beforeend', styles);
        modalStylesAdded = true;
    }

    let sectionsHTML = [];
    const filePathForDisplay = escapeHtml(nodeData.path || nodeData.id);
    sectionsHTML.push(`
        <div class="details-section file-info">
            <h3 class="section-title file-info-title">File: ${escapeHtml(nodeData.name || 'N/A')}</h3>
            <ul>
                <li><strong>Full Path:</strong> ${filePathForDisplay}</li>
                <li><strong>Type:</strong> ${escapeHtml(nodeData.type || 'N/A')}</li>
            </ul>
        </div>`);
    if (nodeData.noDetailedData) {
        sectionsHTML.push('<div class="details-section no-data-warning"><h3 class="section-title">No Detailed Data</h3><p>No detailed scan data was found.</p></div>');
    }
    const formatDep = dep => {
        if (typeof dep === 'string') return escapeHtml(dep);
        const name = escapeHtml(dep.name || 'Unknown');
        const lineInfo = (dep.line && !isNaN(parseInt(dep.line)) && parseInt(dep.line) > 0) ? ` (line ${dep.line})` : '';
        return (dep.isWorker || dep.type === 'worker') 
            ? `<span class="worker-connection">${name}${lineInfo}</span>`
            : `${name}${lineInfo}`;
    };
    sectionsHTML.push(createSectionHTML('Dependencies', nodeData.dependencies, 'dependencies-title', formatDep));
    sectionsHTML.push(createSectionHTML('Dependents', nodeData.dependents, 'dependents-title', formatDep));
    if (nodeData.imports?.length) {
        const workerImports = nodeData.imports.filter(imp => imp.type === 'worker' || imp.isWorker);
        if (workerImports.length) sectionsHTML.push(createSectionHTML('Worker Connections', workerImports, 'workers-title', imp => `<span class="worker-connection">${escapeHtml(imp.name || imp.path || 'Unknown')} ${imp.line ? `(line ${imp.line})` : ''}</span>`));
        const regularImports = nodeData.imports.filter(imp => imp.type !== 'worker' && !imp.isWorker);
        if (regularImports.length) sectionsHTML.push(createSectionHTML('Imports', regularImports, 'imports-title', imp => `${escapeHtml(imp.name || imp.path || 'Unknown')} ${imp.line ? `(line ${imp.line})` : ''}`));
    }
    sectionsHTML.push(createSectionHTML('Exports', nodeData.exports, 'exports-title', exp => `${escapeHtml(exp.name || 'default')} (${escapeHtml(exp.exportType || 'N/A')}) ${exp.line ? `(line ${exp.line})` : ''}`));
    sectionsHTML.push(createSectionHTML('Functions/Classes', nodeData.functions, 'functions-title', fn => `${escapeHtml(fn.name || 'anonymous')} ${fn.type === 'class' ? '(class)' : `(${(fn.params || []).map(p => escapeHtml(p)).join(', ')})`} (line ${fn.line || 'N/A'})`));
    sectionsHTML.push(createSectionHTML('Variables', nodeData.variables, 'variables-title', v => `${escapeHtml(v.name || 'unnamed')} (${escapeHtml(v.type || 'var')}) (line ${v.line || 'N/A'})`));
    sectionsHTML.push(createSectionHTML('Events', nodeData.events, 'events-title', e => `${escapeHtml(e.name || 'unnamed')} (${escapeHtml(e.type || 'event')}) (line ${e.line || 'N/A'}) ${e.handler ? `→ ${escapeHtml(e.handler)}` : ''}`));

    const searchHTML = `
    <div class="search-container" style="padding: 8px; margin-bottom: 8px; position: sticky; top: 0; background: var(--panel-bg, #333); z-index: 10;">
        <input type="text" id="details-search" placeholder="Filter items in this file..." style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid var(--border-color, #555); background: var(--input-bg, #444); color: var(--text-color, #fff);" />
    </div>`;
    
    detailsPanelEl.innerHTML = searchHTML + sectionsHTML.filter(Boolean).join('');
    detailsPanelEl.setAttribute('data-scan-context', 'file-details');
    if (panelContainerEl && (nodeData.path || nodeData.id)) {
        panelContainerEl.setAttribute('data-current-file', nodeData.path || nodeData.id);
    }

    const searchInput = detailsPanelEl.querySelector('#details-search');
    if (searchInput) searchInput.addEventListener('input', handleSearchFilter);

    const sectionClassMap = {
        'functions': 'functions-title', 'variables': 'variables-title', 'imports': 'imports-title',
        'exports': 'exports-title', 'dependencies': 'dependencies-title', 'dependents': 'dependents-title',
        'events': 'events-title', 'workers': 'workers-title'
    };
    detailsPanelEl.querySelectorAll('.section-title').forEach(header => {
        header.addEventListener('click', () => {
            header.classList.toggle('collapsed');
            const content = header.nextElementSibling;
            if (content) content.classList.toggle('collapsed');
        });
        if (header.classList.contains('file-info-title')) {
            header.classList.remove('collapsed');
        } else if (focusProperty && header.classList.contains(sectionClassMap[focusProperty])) {
            header.classList.remove('collapsed');
            const content = header.nextElementSibling;
            if (content) content.classList.remove('collapsed');
            setTimeout(() => header.scrollIntoView({ behavior: 'smooth', block: 'center' }), 100);
        } else {
            header.classList.add('collapsed');
            const content = header.nextElementSibling;
            if (content) content.classList.add('collapsed');
        }
    });
    
    if (panelContainerEl) panelContainerEl.classList.add('open');
    const contentArea = document.querySelector('.content-scroll-area');
    if (contentArea) contentArea.classList.add('panel-open');
};

// Closes details panel or resets to summary
const closeDetailsPanel = () => {
    const panelContainerEl = document.getElementById('details-panel-container'); // Re-fetch
    const detailsPanelEl = document.getElementById('file-details-panel'); // Re-fetch
    const nodeInfoContentEl = document.getElementById('node-info-content'); // Re-fetch

    if (panelContainerEl) {
        panelContainerEl.classList.remove('open');
        panelContainerEl.removeAttribute('data-current-file');
    }
    // Clear the right panel when closing
    if (detailsPanelEl) {
        detailsPanelEl.innerHTML = "<div class='details-section'><h3 class='section-title'>Ready</h3><p>Select a node to view details.</p></div>";
    }
    if (nodeInfoContentEl) {
        nodeInfoContentEl.innerHTML = "<h3>Node Details</h3><p>Click a node in the visualization.</p>";
    }
    const contentArea = document.querySelector('.content-scroll-area');
    if (contentArea) contentArea.classList.remove('panel-open');
};

// Theme Management
const applyTheme = () => {
    document.body.classList.toggle('light-theme-active', isLightTheme);
    const themeIconContainerEl = document.getElementById('theme-icon-container') || document.getElementById('theme-toggle');
    if (themeIconContainerEl) {
        themeIconContainerEl.innerHTML = isLightTheme ? MOON_ICON : SUN_ICON;
    }
};

// Worker Management
const cleanupWorkers = () => {
    activeWorkers.forEach((worker, index) => {
        try {
            // Send cleanup message first
            worker.postMessage({ type: 'cleanup' });
            
            // Give worker time to cleanup, then terminate
            setTimeout(() => {
                try {
                    worker.terminate();
                } catch (e) {
                    console.warn(`Error terminating worker ${index}:`, e);
                }
            }, 100);
        } catch (e) {
            console.error(`Error cleaning up worker ${index}:`, e);
            // Force terminate if cleanup fails
            try {
                worker.terminate();
            } catch (termError) {
                console.error(`Error force terminating worker ${index}:`, termError);
            }
        }
    });
    
    activeWorkers.length = 0; // Clear array
    workerLastActive = {};
    
    // Force garbage collection if available
    if (window.gc) {
        setTimeout(() => window.gc(), 200);
    }
};
const startWorker = (workerScriptPath = './js/core/scanner.worker.js') => {
    const statusEl = document.getElementById('status'); // Re-fetch
    try {
        const worker = new Worker(workerScriptPath);
        
        // Add worker validation
        if (!worker) {
            throw new Error('Worker creation returned null/undefined');
        }
        
        // Add worker ID and tracking
        worker.workerId = activeWorkers.length;
        worker.terminated = false;
        worker.initialized = false;
        
        // Override terminate to track state
        const originalTerminate = worker.terminate.bind(worker);
        worker.terminate = function() {
            this.terminated = true;
            return originalTerminate();
        };
        
        activeWorkers.push(worker);

        return worker;
    } catch (e) {
        if (statusEl) statusEl.textContent = `Error: Could not create worker from ${workerScriptPath}. ${e.message}`;
        console.error("Failed to create worker:", e);
        return null;
    }
};
const distributeFilesToWorkers = async (filesToProcess) => {
    const statusEl = document.getElementById('status');
    const progressBarEl = document.getElementById('progress-bar');
    const progressContainerEl = document.getElementById('progress-bar-container');
    
    try {
        // Initialize enhanced file processor if not already done
        if (!enhancedFileProcessor) {
            enhancedFileProcessor = new EnhancedFileProcessor({
                workerScriptPath: 'js/core/scanner.worker.js',
                maxWorkers: Math.min(navigator.hardwareConcurrency || 4, 4),
                maxFileSize: 10 * 1024 * 1024, // 10MB
                chunkSize: 100,
                memoryThreshold: 0.7,
                enableFallback: true
            });
        }
        
        if (progressContainerEl) progressContainerEl.style.display = 'block';
        
        // Process files using enhanced processor
        const result = await enhancedFileProcessor.processFiles(filesToProcess, {
            progressCallback: (progress) => {
                if (progressBarEl && progressContainerEl) {
                    progressBarEl.style.width = `${progress.percentage || 0}%`;
                    // Remove any text content from progress bar
                    progressBarEl.textContent = '';
                    progressContainerEl.style.display = 'block';
                }
            },
            statusCallback: (status) => {
                if (statusEl) {
                    statusEl.textContent = `Auto-processing: ${status}`;
                }
            }
        });
        
        if (result.success) {
            const scanResult = result.data;
            
            // Store results globally
            window.scanResults = scanResult;
            

            
            // Process results through the app
            if (window.dependencyVisualizerApp?.processScanResults) {

                
                window.dependencyVisualizerApp.processScanResults(scanResult);

                // Force multiple renders to ensure connections are drawn
                setTimeout(() => {
                    if (window.dependencyVisualizerApp?.mindmapVisualizer) {

                        window.dependencyVisualizerApp.mindmapVisualizer.render();
                    }
                }, 100);
                
                setTimeout(() => {
                    if (window.dependencyVisualizerApp?.mindmapVisualizer) {

                        window.dependencyVisualizerApp.mindmapVisualizer.render();
                    }
                }, 500);
            } else {
                console.warn("DependencyVisualizerApp or processScanResults method missing.");
            }
            
            // Populate left panel with scan summary
            populateLeftPanelWithScanResults(scanResult);
            
            if (statusEl) {
                statusEl.textContent = result.fallback
                    ? `Auto-processing complete (fallback mode) - ${result.stats.filesProcessed} files processed`
                    : `Auto-processing complete - ${result.stats.filesProcessed} files processed`;
            }
            
            // Log comprehensive statistics

            
        } else {
            throw new Error(result.error?.message || 'Processing failed');
        }
        
    } catch (error) {
        console.error('Enhanced file processing failed:', error);
        
        if (statusEl) {
            statusEl.textContent = `Auto-processing failed: ${error.message}`;
        }
        
        // Show error to user
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
            background: rgba(244, 67, 54, 0.9); color: white; padding: 15px 20px;
            border-radius: 5px; z-index: 10000; max-width: 80%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        `;
        errorDiv.innerHTML = `
            <strong>Folder Loading Failed</strong><br>
            ${escapeHtml(error.message)}<br>
            <small>Check console for detailed error information</small>
        `;
        document.body.appendChild(errorDiv);
        
        // Auto-remove error after 10 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 10000);
        
    } finally {
        if (progressContainerEl) {
            progressContainerEl.style.display = 'none';
        }
        
        // Log final statistics
        if (enhancedFileProcessor) {
            const finalStats = enhancedFileProcessor.getStats();

        }
    }
};

// --- Legacy SVG Visualization (buildVisualization, transformDataForDependencyGraph) ---
// This section is largely unchanged as errors were not related to its internal logic.
// If DOM.visualization is null, it will console.error and return.
const transformDataForDependencyGraph = (scanResults) => {
    // console.warn("transformDataForDependencyGraph (custom SVG layout) called.");
    const visualizationEl = document.getElementById('visualization-container'); // Re-fetch
    if (!visualizationEl) {
        // console.error("SVG Visualization container ('#visualization-container') not found for transform.");
        return { nodes: [], edges: [] }; // Return empty if container missing
    }
    if (!scanResults?.files || !scanResults.graph) return { nodes: [], edges: [] };
    const fileToNodeMap = new Map();
    const nodes = [];
    const edges = [];
    Object.keys(scanResults.files).forEach((filePath, index) => {
        if (nodes.length >= 300) return;
        const fileData = scanResults.files[filePath];
        const fileName = filePath.split('/').pop() || filePath;
        const node = { id: filePath, label: fileName.length > 20 ? fileName.substring(0, 18) + "..." : fileName, type: fileData?.isEntryPoint ? 'entry' : (fileData?.isWorker ? 'worker' : 'file'), data: fileData };
        nodes.push(node);
        fileToNodeMap.set(filePath, node);
    });
    if (scanResults.graph.edges) {
        scanResults.graph.edges.forEach(edge => {
            if (edges.length >= 500) return;
            if (edge.from && edge.to && fileToNodeMap.has(edge.from) && fileToNodeMap.has(edge.to)) {
                edges.push({ source: fileToNodeMap.get(edge.from), target: fileToNodeMap.get(edge.to), type: edge.type || 'import', connectionType: edge.connectionType });
            }
        });
    }
    return { nodes, edges };
};
const buildVisualization = (scanData) => {
    // console.warn("buildVisualization (custom SVG graph) called.");
    const visualizationEl = document.getElementById('visualization-container'); // Re-fetch
    const statusEl = document.getElementById('status'); // Re-fetch

    if (!visualizationEl) {
        console.error("SVG Visualization container ('#visualization-container') not found.");
        return;
    }
    visualizationEl.innerHTML = '';
    visualizationEl.style.cssText = 'position: relative; width: 100%; height: 100%; background: var(--graph-bg, #2d2d2d); overflow: hidden; cursor: grab;';
    const transformedData = transformDataForDependencyGraph(scanData);
    const graphNodes = transformedData.nodes;
    const graphEdges = transformedData.edges;
    if (!graphNodes || graphNodes.length === 0) {
        visualizationEl.innerHTML = '<div style="text-align: center; padding: 50px; color: var(--text-color, #fff);">No data for custom SVG.</div>';
        if (statusEl) statusEl.textContent = 'No data for custom SVG visualization.';
        return;
    }
    if (statusEl) statusEl.textContent = 'Preparing custom SVG visualization...';
    const svg = document.createElementNS(SVG_NS, 'svg');
    svg.setAttribute('width', '100%'); svg.setAttribute('height', '100%');
    visualizationEl.appendChild(svg);
    const defs = document.createElementNS(SVG_NS, 'defs');
    const marker = document.createElementNS(SVG_NS, 'marker');
    marker.setAttribute('id', 'svg-arrowhead'); marker.setAttribute('markerWidth', '10'); marker.setAttribute('markerHeight', '7');
    marker.setAttribute('refX', '9.5'); marker.setAttribute('refY', '3.5'); marker.setAttribute('orient', 'auto-start-reverse');
    const arrowPath = document.createElementNS(SVG_NS, 'path');
    arrowPath.setAttribute('d', 'M0,0 L10,3.5 L0,7 Z'); arrowPath.style.fill = 'var(--edge-arrow-color, #ccc)';
    marker.appendChild(arrowPath); defs.appendChild(marker); svg.appendChild(defs);
    const g = document.createElementNS(SVG_NS, 'g'); svg.appendChild(g);
    const numNodes = graphNodes.length;
    const canvasWidth = visualizationEl.clientWidth || 800;
    const canvasHeight = visualizationEl.clientHeight || 600;
    const radius = Math.min(canvasWidth, canvasHeight) / 2 * 0.8;
    const centerX = canvasWidth / 2; const centerY = canvasHeight / 2;
    graphNodes.forEach((node, i) => {
        const angle = (i / numNodes) * 2 * Math.PI;
        node.x = centerX + radius * Math.cos(angle); node.y = centerY + radius * Math.sin(angle);
        const nodeGroup = document.createElementNS(SVG_NS, 'g');
        nodeGroup.setAttribute('transform', `translate(${node.x},${node.y})`);
        nodeGroup.classList.add('graph-node-svg'); nodeGroup.dataset.nodeId = node.id; nodeGroup.style.cursor = 'pointer';
        g.appendChild(nodeGroup);
        const rectWidth = 120, rectHeight = 35;
        const rect = document.createElementNS(SVG_NS, 'rect');
        rect.setAttribute('x', -rectWidth/2); rect.setAttribute('y', -rectHeight/2);
        rect.setAttribute('width', rectWidth); rect.setAttribute('height', rectHeight); rect.setAttribute('rx', 5);
        const fillColor = node.type === 'entry' ? 'var(--node-entry-bg, #4A90E2)' : node.type === 'worker' ? 'var(--node-worker-bg, #F5A623)' : 'var(--node-file-bg, #7ED321)';
        rect.style.fill = fillColor; rect.style.stroke = 'var(--node-border-color, #333)'; rect.style.strokeWidth = '1.5';
        nodeGroup.appendChild(rect);
        const textEl = document.createElementNS(SVG_NS, 'text'); // Renamed to avoid conflict
        textEl.setAttribute('text-anchor', 'middle'); textEl.setAttribute('dominant-baseline', 'middle');
        textEl.style.fill = 'var(--node-text-color, #fff)'; textEl.style.fontSize = '10px'; textEl.style.pointerEvents = 'none';
        textEl.textContent = node.label; nodeGroup.appendChild(textEl);
        nodeGroup.addEventListener('click', () => {
            if (window.dependencyVisualizerApp?.selectNodeAndOpenFileDetails) {
                window.dependencyVisualizerApp.selectNodeAndOpenFileDetails(node.id);
            } else {
                 openDetailsPanel(node.data || { id: node.id, name: node.label, path: node.id, type: node.type });
            }
        });
        nodeGroup.addEventListener('mouseenter', () => rect.style.strokeWidth = '3');
        nodeGroup.addEventListener('mouseleave', () => rect.style.strokeWidth = '1.5');
    });
    graphEdges.forEach(edge => {
        if (edge.source && edge.target) {
            const line = document.createElementNS(SVG_NS, 'line');
            line.setAttribute('x1', edge.source.x); line.setAttribute('y1', edge.source.y);
            line.setAttribute('x2', edge.target.x); line.setAttribute('y2', edge.target.y);
            line.style.stroke = edge.connectionType === 'worker' ? 'var(--edge-worker-color, orange)' : 'var(--edge-color, #aaa)';
            line.style.strokeWidth = '1.5'; line.setAttribute('marker-end', 'url(#svg-arrowhead)');
            g.insertBefore(line, g.firstChild);
        }
    });
    let viewBox = { x: 0, y: 0, w: canvasWidth, h: canvasHeight };
    svg.setAttribute('viewBox', `${viewBox.x} ${viewBox.y} ${viewBox.w} ${viewBox.h}`);

    // Skip adding legacy zoom/pan if this is a mindmap SVG (it has its own zoom/pan)
    if (svg.getAttribute('data-mindmap-zoom-pan') === 'true') {

        if (statusEl) statusEl.textContent = 'Mindmap SVG Visualization ready. Using mindmap zoom/pan.';
        return;
    }

    let isPanning = false; let panStartPoint = { x: 0, y: 0 };
    svg.addEventListener('mousedown', (e) => { if (e.button !== 0) return; isPanning = true; panStartPoint = { x: e.clientX, y: e.clientY }; svg.style.cursor = 'grabbing'; });
    svg.addEventListener('mousemove', (e) => { if (!isPanning) return; const dx = (panStartPoint.x - e.clientX) * (viewBox.w / svg.clientWidth); const dy = (panStartPoint.y - e.clientY) * (viewBox.h / svg.clientHeight); viewBox.x += dx; viewBox.y += dy; svg.setAttribute('viewBox', `${viewBox.x} ${viewBox.y} ${viewBox.w} ${viewBox.h}`); panStartPoint = { x: e.clientX, y: e.clientY }; });
    const stopPanning = () => { isPanning = false; svg.style.cursor = 'grab'; };
    svg.addEventListener('mouseup', stopPanning); svg.addEventListener('mouseleave', stopPanning);
    svg.addEventListener('wheel', (e) => { e.preventDefault(); const zoomIntensity = 0.1; const { clientX, clientY } = e; const svgRect = svg.getBoundingClientRect(); const mouseXInSVG = viewBox.x + (clientX - svgRect.left) * (viewBox.w / svg.clientWidth); const mouseYInSVG = viewBox.y + (clientY - svgRect.top) * (viewBox.h / svg.clientHeight); const zoomFactor = e.deltaY < 0 ? (1 - zoomIntensity) : (1 + zoomIntensity); viewBox.w *= zoomFactor; viewBox.h *= zoomFactor; viewBox.x = mouseXInSVG - (clientX - svgRect.left) * (viewBox.w / svg.clientWidth); viewBox.y = mouseYInSVG - (clientY - svgRect.top) * (viewBox.h / svg.clientHeight); svg.setAttribute('viewBox', `${viewBox.x} ${viewBox.y} ${viewBox.w} ${viewBox.h}`); }, { passive: false });
    if (statusEl) statusEl.textContent = 'Custom SVG Visualization ready. Zoom/Pan enabled.';
};
// --- End of Legacy SVG Visualization ---

// Event Handlers & Initialization
document.addEventListener('DOMContentLoaded', () => {
    // Re-fetch DOM elements now that DOM is loaded to ensure they are the latest references
    DOM.projectFolderInput = document.getElementById('project-folder-input');
    DOM.progressBarContainer = document.getElementById('progress-bar-container');
    DOM.progressBar = document.getElementById('progress-bar');
    DOM.status = document.getElementById('status');
    DOM.fileDetailsPanel = document.getElementById('file-details-panel');
    DOM.closeDetailsPanelBtn = document.getElementById('close-details-panel');
    DOM.codePreviewModal = document.getElementById('code-preview-modal');
    DOM.themeToggleButton = document.getElementById('theme-toggle');
    DOM.themeIconContainer = document.getElementById('theme-icon-container');
    DOM.visualization = document.getElementById('visualization-container');
    DOM.appContainer = document.getElementById('visualization-container'); // Crucial for App init
    DOM.panelContainer = document.getElementById('details-panel-container');
    DOM.nodeInfoContent = document.getElementById('node-info-content');

    // Crucial HTML element checks
    if (!DOM.appContainer) {
        console.error("FATAL: Application container ('#visualization-container') not found in HTML. Visualization cannot be initialized.");
        if (DOM.status) DOM.status.innerHTML = "<strong style='color:red;'>Error: Missing #visualization-container in HTML. App cannot start.</strong>";
        return; // Stop further initialization if core container is missing
    }
    if (!DOM.projectFolderInput) {
        console.warn("Warning: Project folder input ('#project-folder') not found in HTML. File selection will not work.");
        if (DOM.status) DOM.status.textContent = "Status: Project folder input missing.";
    }


    applyTheme();

    if (DOM.closeDetailsPanelBtn) {
        DOM.closeDetailsPanelBtn.addEventListener('click', closeDetailsPanel);
    }

    if (DOM.themeToggleButton) {
        DOM.themeToggleButton.addEventListener('click', () => {
            isLightTheme = !isLightTheme;
            localStorage.setItem(THEME_KEY, isLightTheme ? 'light' : 'dark');
            applyTheme();
            document.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme: isLightTheme ? 'light' : 'dark' }}));
        });
    }

    if (DOM.codePreviewModal) {
        const closeButton = DOM.codePreviewModal.querySelector('.close');
        if (closeButton) {
            closeButton.addEventListener('click', () => DOM.codePreviewModal.style.display = 'none');
        }
        DOM.codePreviewModal.addEventListener('click', (e) => {
            if (e.target === DOM.codePreviewModal) DOM.codePreviewModal.style.display = 'none';
        });
        window.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && DOM.codePreviewModal && DOM.codePreviewModal.style.display === 'block') {
                DOM.codePreviewModal.style.display = 'none';
            }
        });
    }

    if (DOM.projectFolderInput) {
        DOM.projectFolderInput.addEventListener('change', async (event) => {
            const files = event.target.files;
            if (!files || files.length === 0) {
                if (DOM.status) DOM.status.textContent = 'No folder selected or folder is empty.';
                return;
            }
            
            if (DOM.status) DOM.status.textContent = 'Auto-processing: Preparing files...';
            if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'block';
            if (DOM.progressBar) {
                DOM.progressBar.style.width = '0%';
                // Remove any text content from progress bar
                DOM.progressBar.textContent = '';
            }
            await loadExcludeRules();
            const originalFileCount = files.length;

            // Filter files and immediately clean up excluded files from memory
            const filesToProcess = [];
            const excludedFilesList = [];

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const relPath = (file.webkitRelativePath || file.name || '').toLowerCase().replace(/\\/g, '/');

                if (!relPath) {
                    excludedFilesList.push(file);
                    continue;
                }

                const pathParts = relPath.split('/');

                // Check folder exclusions
                const isExcludedFolder = excludedFolders.some(excludedFolder => {
                    const lowerExcludedFolder = excludedFolder.toLowerCase().trim();
                    if (!lowerExcludedFolder) return false;
                    return pathParts.includes(lowerExcludedFolder) ||
                           relPath.startsWith(lowerExcludedFolder + '/') ||
                           relPath.includes('/' + lowerExcludedFolder + '/');
                });

                if (isExcludedFolder) {
                    excludedFilesList.push(file);
                    continue;
                }

                // Check file exclusions
                const fileName = file.name.toLowerCase();
                const isExcludedFile = excludedFiles.some(pattern => {
                    if (pattern.includes('*')) {
                        const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
                        return regex.test(fileName);
                    }
                    return fileName === pattern.toLowerCase();
                });

                if (isExcludedFile) {
                    excludedFilesList.push(file);
                    continue;
                }

                // Check allowed extensions
                const allowedExtensions = ['.js', '.mjs', '.ts', '.jsx', '.tsx', '.html', '.htm', '.vue', '.svelte', '.json', '.css', '.scss', '.sass', '.less'];
                if (allowedExtensions.some(ext => fileName.endsWith(ext))) {
                    filesToProcess.push(file);
                } else {
                    excludedFilesList.push(file);
                }
            }

            // Immediately clean up excluded files from memory
            const excludedCount = cleanupFileReferences(excludedFilesList, `Main.js (${originalFileCount} → ${filesToProcess.length})`);
            if (excludedCount > 0) {

            }

            totalFilesExpected = filesToProcess.length;
            if (DOM.status) {
                const statusText = filesToProcess.length
                    ? `Found ${filesToProcess.length} files${excludedCount > 0 ? ` (excluded ${excludedCount})` : ''}. Auto-processing started...`
                    : 'No relevant files found. Check exclusion rules/folder.';
                DOM.status.textContent = statusText;
            }
            if (filesToProcess.length > 0) {
                // Show progress bar immediately to indicate auto-processing
                if (DOM.progressBarContainer) {
                    DOM.progressBarContainer.style.display = 'block';
                }
                if (DOM.progressBar) {
                    DOM.progressBar.style.width = '0%';
                    // Remove any text content from progress bar
                    DOM.progressBar.textContent = '';
                }
                distributeFilesToWorkers(filesToProcess);
            } else {
                if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'none';
            }
        });
    }
    // else: Warning for missing projectFolderInput already logged if DOM.status exists.

    // Initialize the main application using mindmap-vanilla.js
    try {
        // Make sure the container element exists before initializing
        const visualizationContainer = document.getElementById('visualization-container');
        if (!visualizationContainer) {
            throw new Error("Container element with ID 'visualization-container' not found in the DOM");
        }
        
        // Wait for MindmapProject to be available
        if (typeof window.MindmapProject === 'undefined') {
            console.warn('MindmapProject not yet available, waiting...');
            // Try again after a short delay
            setTimeout(() => {
                if (typeof window.MindmapProject !== 'undefined') {
                    initializeApp();
                } else {
                    console.error('MindmapProject still not available after delay');
                }
            }, 100);
            return;
        }
        
        initializeApp();
        
    } catch (error) {
        console.error("Fatal Error: Failed to initialize DependencyVisualizerApp.", error);
        if (DOM.status) DOM.status.innerHTML = `<strong style='color:red;'>Error: App initialization failed. ${escapeHtml(error.message)}. See console.</strong>`;
        
        const errorDiv = document.createElement('div');
        errorDiv.innerHTML = `<strong>Application Initialization Failed</strong><br>${escapeHtml(error.message)}<br><small>Please check the developer console and ensure required HTML elements exist.</small>`;
        errorDiv.style.cssText = "color: red; background: #fff0f0; border: 1px solid red; padding: 15px; margin: 10px; border-radius: 5px;";
        (DOM.appContainer || document.body).prepend(errorDiv);
    }
    
    function initializeApp() {
        const visualizationContainer = document.getElementById('visualization-container');
        
        const appConfig = {
            container: visualizationContainer,
            detailsPanel: 'details-panel-container',
            scannerWorkerPath: 'js/core/scanner.worker.js'
        };

        const app = new DependencyVisualizerApp(appConfig);
        app.initialize();
        
        // Make the app instance globally accessible
        window.app = app;
        window.dependencyVisualizerApp = app;

        if (DOM.status) {
            if (DOM.projectFolderInput) {
                DOM.status.textContent = "Mindmap visualizer initialized. Select a project folder to begin analysis.";
            } else {
                DOM.status.textContent = "Mindmap visualizer initialized, but project folder input is missing.";
            }
        }
        

    }
});
// Cleanup on page unload
window.addEventListener('beforeunload', async () => {

    
    if (enhancedFileProcessor) {
        try {
            await enhancedFileProcessor.cleanup();
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }
    
    // Clean up legacy workers if any
    cleanupWorkers();
});

// Handle visibility change for memory management
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden, trigger memory cleanup
        if (enhancedFileProcessor) {
            const memoryManager = enhancedFileProcessor.memoryManager;
            if (memoryManager) {
                memoryManager.manualCleanup('medium');
            }
        }
        
        // Force garbage collection if available
        if (window.gc) {
            setTimeout(() => window.gc(), 1000);
        }
    }
});