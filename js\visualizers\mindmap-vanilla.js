class MindmapProject {
    constructor(svgElement = null, externalData = null) {
        this.svgElement = svgElement;
        this.externalData = externalData;
        this.dragState = {
            isDragging: false,
            draggedNodeId: null,
            offset: { x: 0, y: 0 },
        };
        this.selectedNodeId = null; // Track selected node for focus mode
        this.nodes = [];
        this.connections = [];
        this.workerConnections = [];
        this.eventConnections = [];
        
        // Line visibility controller
        this.lineVisibilityController = null;

        // Zoom and Pan properties - adjusted for proper spacing layout
        this.viewBox = { x: 0, y: 0, width: 1100, height: 500 }; // Optimized for new layout spacing
        this.isPanning = false;
        this.panStartPoint = { x: 0, y: 0 };
        this.zoomLevel = 1;
        this.minZoom = 0.1;
        this.maxZoom = 5;

        // Minimap properties
        this.minimapContainer = null;
        this.minimapSvg = null;
        this.minimapScale = 0.15;
        this.minimapWidth = 200;
        this.minimapHeight = 150;
        this.viewportRect = null;
        this.isMinimapDragging = false;

        // Content bounds for proper minimap scaling - updated for new layout
        this.contentBounds = { minX: 0, minY: 0, maxX: 1100, maxY: 500 };
        this.minimapContentScale = 1;
        this.minimapOffsetX = 0;
        this.minimapOffsetY = 0;

        // Bind methods to preserve 'this' context BEFORE using them
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleMinimapMouseDown = this.handleMinimapMouseDown.bind(this);
        this.handleMinimapMouseMove = this.handleMinimapMouseMove.bind(this);
        this.handleMinimapMouseUp = this.handleMinimapMouseUp.bind(this);

        // Bind zoom and pan methods
        this.handlePanStart = this.handlePanStart.bind(this);
        this.handlePanMove = this.handlePanMove.bind(this);
        this.handlePanEnd = this.handlePanEnd.bind(this);
        this.handleWheel = this.handleWheel.bind(this);

        this.initializeMindmap();

        // If an external SVG element is provided, set up zoom and pan
        if (this.svgElement) {
            this.setupZoomAndPan(this.svgElement);
            this.addZoomControlsToContainer();
            this.updateViewBox(); // Set initial viewBox

            // Initialize minimap after a short delay to ensure DOM is ready
            setTimeout(() => {
                this.createMinimap();
                this.renderMinimap();
                
                // Initialize line visibility controller after render
                this.initializeLineVisibilityController();

                // Start with all lines hidden (they will show when nodes are selected)
                if (this.lineVisibilityController) {
                    this.lineVisibilityController.hideAllLinesForNodeSelection();
                }
            }, 100);
        }
    }

    initializeMindmap(externalData = null) {
        const dataToUse = externalData || this.externalData;
        const mindmapData = this.createMindmapData(dataToUse);
        this.nodes = mindmapData.nodes;
        this.connections = mindmapData.connections;
        this.workerConnections = mindmapData.workerConnections;
        this.eventConnections = mindmapData.eventConnections;
        
        // Update line visibility controller if it exists
        if (this.lineVisibilityController) {
            this.lineVisibilityController.updateData();
        }
    }

    createMindmapData(externalData = null) {
        // If external data is provided, use it
        if (externalData) {
            return this.processExternalData(externalData);
        }

        // Fallback to hardcoded data if no external data provided
        const nodes = [
            {
                id: 'enhanced-file-reader',
                text: 'Enhanced File Reader',
                filename: 'reader.js',
                x: 25,
                y: 260, // Centered vertically among level 1 nodes
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'streaming-processor',
                text: 'Streaming Processor',
                filename: 'stream.js',
                x: 220,
                y: 155, // Properly spaced level 1 node
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'size-validation',
                text: 'Size Validation',
                filename: 'validate.json',
                x: 220,
                y: 225, // Evenly spaced from streaming processor
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: false,
            },
            {
                id: 'permission-checks',
                text: 'Permission Checks',
                filename: 'auth.html',
                x: 220,
                y: 295, // Evenly spaced from size validation
                width: 120,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: false,
            },
            {
                id: 'timeout-protection',
                text: 'Timeout Protection',
                filename: 'timeout.js',
                x: 220,
                y: 365, // Evenly spaced from permission checks
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'resilient-worker-pool',
                text: 'Resilient Worker Pool',
                filename: 'workers.js',
                x: 420,
                y: 120, // Level 2 - properly aligned
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'chunked-processing',
                text: 'Chunked Processing',
                filename: 'chunks.css',
                x: 420,
                y: 225, // Level 2 - evenly spaced from resilient worker pool
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'backpressure-control',
                text: 'Backpressure Control',
                filename: 'pressure.js',
                x: 420,
                y: 330, // Level 2 - evenly spaced from chunked processing
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'memory-safe-parser',
                text: 'Memory-Safe Parser',
                filename: 'parser.js',
                x: 620,
                y: 85, // Level 3 - properly aligned
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'worker-health-monitor',
                text: 'Worker Health Monitor',
                filename: 'health.html',
                x: 620,
                y: 176, // Level 3 - evenly spaced
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'graceful-termination',
                text: 'Graceful Termination',
                filename: 'shutdown.json',
                x: 620,
                y: 267, // Level 3 - evenly spaced
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'error-recovery',
                text: 'Error Recovery',
                filename: 'errors.css',
                x: 620,
                y: 358, // Level 3 - evenly spaced
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'progressive-results',
                text: 'Progressive Results',
                filename: 'results.svg',
                x: 820,
                y: 50, // Level 4 - top node, properly aligned
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'incremental-aggregator',
                text: 'Incremental Aggregator',
                filename: 'aggregate.js',
                x: 820,
                y: 120, // Level 4 - evenly spaced (70px spacing)
                width: 150,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'memory-cleanup',
                text: 'Memory Cleanup',
                filename: 'cleanup.img',
                x: 820,
                y: 190, // Level 4 - evenly spaced (70px spacing)
                width: 120,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'regex-timeouts',
                text: 'Regex Timeouts',
                filename: 'regex.json',
                x: 820,
                y: 260, // Level 4 - evenly spaced (70px spacing)
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'memory-limits',
                text: 'Memory Limits',
                filename: 'limits.css',
                x: 820,
                y: 330, // Level 4 - evenly spaced (70px spacing)
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'corruption-detection',
                text: 'Corruption Detection',
                filename: 'detect.html',
                x: 820,
                y: 400, // Level 4 - evenly spaced (70px spacing)
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
        ];

        // Connection definitions
        const connections = [
            { from: 'enhanced-file-reader', to: 'streaming-processor' },
            { from: 'enhanced-file-reader', to: 'size-validation' },
            { from: 'enhanced-file-reader', to: 'permission-checks' },
            { from: 'enhanced-file-reader', to: 'timeout-protection' },
            { from: 'streaming-processor', to: 'resilient-worker-pool' },
            { from: 'streaming-processor', to: 'chunked-processing' },
            { from: 'size-validation', to: 'backpressure-control' },
            { from: 'resilient-worker-pool', to: 'memory-safe-parser' },
            { from: 'resilient-worker-pool', to: 'worker-health-monitor' },
            { from: 'chunked-processing', to: 'graceful-termination' },
            { from: 'backpressure-control', to: 'error-recovery' },
            { from: 'memory-safe-parser', to: 'progressive-results' },
            { from: 'memory-safe-parser', to: 'incremental-aggregator' },
            { from: 'memory-safe-parser', to: 'memory-cleanup' },
            { from: 'memory-safe-parser', to: 'regex-timeouts' },
            { from: 'worker-health-monitor', to: 'memory-limits' },
            { from: 'graceful-termination', to: 'corruption-detection' },
        ];

        // Special worker creation connections
        const workerConnections = [
            { from: 'enhanced-file-reader', to: 'streaming-processor', type: 'worker' },
            { from: 'streaming-processor', to: 'resilient-worker-pool', type: 'worker' },
            { from: 'resilient-worker-pool', to: 'memory-safe-parser', type: 'worker' },
            { from: 'memory-safe-parser', to: 'incremental-aggregator', type: 'worker' },
        ];

        // Event flow connections
        const eventConnections = [
            { from: 'timeout-protection', to: 'backpressure-control', type: 'event' },
            { from: 'worker-health-monitor', to: 'graceful-termination', type: 'event' },
            { from: 'graceful-termination', to: 'error-recovery', type: 'event' },
        ];

        return { nodes, connections, workerConnections, eventConnections };
    }

    /**
     * Process external data and convert it to mindmap format
     * @param {Object} externalData - External data object
     * @param {Array} externalData.nodes - Array of node objects from scanner
     * @param {Array} externalData.links - Array of link objects from scanner
     * @returns {Object} - Processed mindmap data
     */
    processExternalData(externalData) {
        const { nodes: externalNodes = [], links: externalLinks = [] } = externalData;

        // Process external data - debug logging removed for performance
        
        // Convert external nodes to mindmap format with hierarchical layout
        const nodes = this.createHierarchicalLayout(externalNodes, externalLinks);

        // Convert external links to connections using actual IDs/paths
        const connections = externalLinks
            .filter(link => link.source && link.target)
            .map(link => ({
                from: String(link.source), // Ensure string format
                to: String(link.target),   // Ensure string format
                type: link.type || 'dependency'
            }));

        // Create a map of node IDs for faster lookup
        const nodeIdMap = new Map();
        nodes.forEach(node => {
            nodeIdMap.set(String(node.id), node);
            // Also map by path if different from ID
            if (node.path && String(node.path) !== String(node.id)) {
                nodeIdMap.set(String(node.path), node);
            }
        });

        // Filter connections to only include those where both nodes exist
        const validConnections = connections.filter(conn => {
            const sourceNode = nodeIdMap.get(conn.from);
            const targetNode = nodeIdMap.get(conn.to);
            const isValid = sourceNode && targetNode;
            if (!isValid) {
                console.warn('MindmapProject.processExternalData - Invalid connection (missing nodes):', {
                    connection: conn,
                    sourceExists: !!sourceNode,
                    targetExists: !!targetNode,
                    availableNodeIds: Array.from(nodeIdMap.keys()).slice(0, 10)
                });
            }
            return isValid;
        });



        // Separate worker and event connections based on link types and node properties
        const workerConnections = [];
        const eventConnections = [];

        validConnections.forEach(conn => {
            const sourceNode = nodeIdMap.get(conn.from);
            const targetNode = nodeIdMap.get(conn.to);
            
            if (sourceNode && targetNode) {
                // Determine connection type based on link type or node capabilities
                if (conn.type === 'worker' || (sourceNode.hasWorker && targetNode.hasWorker)) {
                    workerConnections.push({ ...conn, type: 'worker' });
                } else if (conn.type === 'event' || (sourceNode.hasEvent && targetNode.hasEvent)) {
                    eventConnections.push({ ...conn, type: 'event' });
                }
            }
        });



        return { nodes, connections: validConnections, workerConnections, eventConnections };
    }

    /**
     * Create hierarchical layout for external nodes that matches the demo pattern exactly
     * @param {Array} externalNodes - Array of node objects from scanner
     * @param {Array} externalLinks - Array of link objects from scanner
     * @returns {Array} - Array of positioned nodes
     */
    createHierarchicalLayout(externalNodes, externalLinks) {



        // Convert nodes to mindmap format first, preserving all scanner data
        const nodes = externalNodes.map((node, index) => ({
            // Preserve all original scanner data
            ...node,
            // Mindmap-specific properties
            id: node.id || node.path || `node-${index}`,
            text: node.name || this.extractFilename(node.path || ''),
            filename: this.extractFilename(node.path || ''),
            width: this.calculateNodeWidth(node.name || this.extractFilename(node.path || '')),
            height: 40,
            hasImport: this.determineHasImport(node),
            hasExport: this.determineHasExport(node),
            hasEvent: this.determineHasEvent(node),
            hasWorker: this.determineHasWorker(node),
            originalNode: node,
            // Temporary positioning - will be updated by layout algorithm
            x: 0,
            y: 0,
            // Layout properties
            exportCount: this.getExportCount(node),
            // Preserve existing dependencies/dependents or initialize empty arrays
            dependencies: node.dependencies || [],
            dependents: node.dependents || []
        }));



        // Build dependency graph for connection tracking
        const nodeMap = new Map(nodes.map(n => [n.id, n]));

        externalLinks.forEach(link => {
            const sourceNode = nodeMap.get(link.source);
            const targetNode = nodeMap.get(link.target);

            if (sourceNode && targetNode) {
                sourceNode.dependents.push(targetNode.id);
                targetNode.dependencies.push(sourceNode.id);
            }
        });

        // Apply the demo-specific layout pattern
        this.applyDemoLayoutPattern(nodes, externalLinks);

        // Ensure all nodes have valid positions (especially HTML nodes)
        // Use independent positioning for each file type to prevent overlaps
        const typeCounters = {};
        nodes.forEach((node) => {
            if (node.x === undefined || node.y === undefined || (node.x === 0 && node.y === 0)) {
                const nodeType = (node.type || '').toLowerCase();


                // Initialize counter for this type if not exists
                let typeGroup = nodeType;
                if (nodeType === 'html' || nodeType === 'htm') {
                    typeGroup = 'html';
                } else if (nodeType === 'css' || nodeType === 'scss' || nodeType === 'sass' || nodeType === 'less') {
                    typeGroup = 'css';
                } else if (nodeType === 'js' || nodeType === 'mjs' || nodeType === 'jsx' || nodeType === 'ts' || nodeType === 'tsx') {
                    typeGroup = 'js';
                }

                if (!typeCounters[typeGroup]) {
                    typeCounters[typeGroup] = 0;
                }
                const typeIndex = typeCounters[typeGroup];
                typeCounters[typeGroup]++;

                // Position each file type in its own area
                if (typeGroup === 'html') {
                    // HTML files: Top-left area
                    node.x = 50 + (typeIndex % 3) * 180;
                    node.y = 50 + Math.floor(typeIndex / 3) * 70;
                } else if (typeGroup === 'css') {
                    // CSS files: Top-center area
                    node.x = 350 + (typeIndex % 3) * 180;
                    node.y = 50 + Math.floor(typeIndex / 3) * 70;
                } else if (typeGroup === 'js') {
                    // JS/TS files: Top-right area
                    node.x = 650 + (typeIndex % 3) * 180;
                    node.y = 50 + Math.floor(typeIndex / 3) * 70;
                } else {
                    // Other file types: Bottom area
                    node.x = 50 + (typeIndex % 4) * 200;
                    node.y = 300 + Math.floor(typeIndex / 4) * 80;
                }
            }
        });

        // Apply auto-alignment to ensure proper spacing and prevent overlaps
        this.autoAlignNodes(nodes, externalLinks);

        return nodes;
    }

    /**
     * Auto-alignment function with neighbor awareness algorithm
     * Detects overlapping nodes, maintains hierarchical layouts, prevents collisions
     * @param {Array} nodes - Array of nodes to align
     * @param {Array} externalLinks - Array of link objects for dependency analysis
     */
    autoAlignNodes(nodes, externalLinks) {
        if (!nodes || nodes.length === 0) return;

        // Configuration for auto-alignment
        const alignConfig = {
            minSpacing: 20,        // Minimum spacing between nodes
            verticalSpacing: 70,   // Preferred vertical spacing
            horizontalSpacing: 200, // Preferred horizontal spacing
            collisionPadding: 10,  // Extra padding to prevent tight overlaps
            maxIterations: 5       // Maximum alignment iterations
        };

        // Group nodes by their hierarchical level (x-coordinate)
        const nodesByLevel = this.groupNodesByLevel(nodes);

        // Perform multiple iterations to resolve all overlaps
        for (let iteration = 0; iteration < alignConfig.maxIterations; iteration++) {
            let hasOverlaps = false;

            // Check and resolve overlaps within each level
            Object.keys(nodesByLevel).forEach(level => {
                const levelNodes = nodesByLevel[level];
                if (levelNodes.length <= 1) return;

                // Sort nodes by Y position for consistent processing
                levelNodes.sort((a, b) => a.y - b.y);

                // Detect and resolve overlaps
                for (let i = 0; i < levelNodes.length - 1; i++) {
                    const currentNode = levelNodes[i];
                    const nextNode = levelNodes[i + 1];

                    if (this.nodesOverlap(currentNode, nextNode, alignConfig)) {
                        hasOverlaps = true;
                        this.resolveNodeOverlap(currentNode, nextNode, alignConfig);
                    }
                }
            });

            // If no overlaps found, we're done
            if (!hasOverlaps) break;

            // Update the grouping for next iteration
            Object.assign(nodesByLevel, this.groupNodesByLevel(nodes));
        }

        // Final pass: ensure minimum spacing between all nodes
        this.enforceMinimumSpacing(nodes, alignConfig);
    }

    /**
     * Group nodes by their hierarchical level (x-coordinate)
     * @param {Array} nodes - Array of nodes
     * @returns {Object} - Object with level as key and array of nodes as value
     */
    groupNodesByLevel(nodes) {
        const nodesByLevel = {};

        nodes.forEach(node => {
            const level = Math.round(node.x / 50) * 50; // Round to nearest 50px for grouping
            if (!nodesByLevel[level]) {
                nodesByLevel[level] = [];
            }
            nodesByLevel[level].push(node);
        });

        return nodesByLevel;
    }

    /**
     * Check if two nodes overlap
     * @param {Object} node1 - First node
     * @param {Object} node2 - Second node
     * @param {Object} config - Alignment configuration
     * @returns {boolean} - True if nodes overlap
     */
    nodesOverlap(node1, node2, config) {
        const node1Right = node1.x + (node1.width || 140);
        const node1Bottom = node1.y + (node1.height || 40);
        const node2Right = node2.x + (node2.width || 140);
        const node2Bottom = node2.y + (node2.height || 40);

        // Check for overlap with padding
        const horizontalOverlap = !(node1Right + config.minSpacing < node2.x ||
                                   node2Right + config.minSpacing < node1.x);
        const verticalOverlap = !(node1Bottom + config.minSpacing < node2.y ||
                                 node2Bottom + config.minSpacing < node1.y);

        return horizontalOverlap && verticalOverlap;
    }

    /**
     * Resolve overlap between two nodes
     * @param {Object} node1 - First node (higher in the layout)
     * @param {Object} node2 - Second node (lower in the layout)
     * @param {Object} config - Alignment configuration
     */
    resolveNodeOverlap(node1, node2, config) {
        const node1Bottom = node1.y + (node1.height || 40);
        const requiredSpacing = config.verticalSpacing + config.collisionPadding;

        // Move the second node down to prevent overlap
        const newY = node1Bottom + requiredSpacing;

        // Only move if it would actually improve the spacing
        if (newY > node2.y) {
            node2.y = newY;
        }
    }

    /**
     * Enforce minimum spacing between all nodes as a final cleanup
     * @param {Array} nodes - Array of all nodes
     * @param {Object} config - Alignment configuration
     */
    enforceMinimumSpacing(nodes, config) {
        // Sort all nodes by Y position for systematic spacing enforcement
        const sortedNodes = [...nodes].sort((a, b) => a.y - b.y);

        for (let i = 0; i < sortedNodes.length - 1; i++) {
            const currentNode = sortedNodes[i];

            // Check against all subsequent nodes
            for (let j = i + 1; j < sortedNodes.length; j++) {
                const otherNode = sortedNodes[j];

                // Only check nodes that are close horizontally
                const horizontalDistance = Math.abs(currentNode.x - otherNode.x);
                if (horizontalDistance < config.horizontalSpacing) {

                    if (this.nodesOverlap(currentNode, otherNode, config)) {
                        // Calculate required vertical separation
                        const currentBottom = currentNode.y + (currentNode.height || 40);
                        const requiredY = currentBottom + config.minSpacing + config.collisionPadding;

                        if (otherNode.y < requiredY) {
                            otherNode.y = requiredY;
                        }
                    }
                }
            }
        }
    }

    /**
     * Apply the demo-specific layout pattern that matches the reference image exactly
     * @param {Array} nodes - Array of nodes to position
     * @param {Array} externalLinks - Array of link objects for dependency analysis
     */
    applyDemoLayoutPattern(nodes, externalLinks) {
        // Define the exact layout pattern from the demo image with proper spacing and alignment
        // This creates a deterministic layout that matches the demo every time

        // Layout configuration for proper spacing and alignment
        const layoutConfig = {
            // Column positions (x-coordinates) - evenly spaced for clean alignment
            columns: {
                root: 25,        // Root node (Enhanced File Reader)
                level1: 220,     // First level dependencies
                level2: 420,     // Second level dependencies
                level3: 620,     // Third level dependencies
                level4: 820      // Final level dependencies
            },
            // Row spacing configuration
            rows: {
                baseY: 50,       // Starting Y position
                spacing: 70,     // Vertical spacing between nodes
                centerOffset: 25 // Offset for centering within groups
            },
            // Node dimensions for proper spacing calculations
            nodeWidth: 140,
            nodeHeight: 40
        };

        // First, identify the root node (Enhanced File Reader) but don't modify actual files
        let rootNode = nodes.find(n =>
            n.filename.toLowerCase().includes('enhanced') ||
            n.filename.toLowerCase().includes('reader') ||
            n.text.toLowerCase().includes('enhanced') ||
            n.text.toLowerCase().includes('reader')
        );

        // Don't rename actual files - preserve their original names
        // The demo layout pattern should only apply to nodes that actually match the demo names

        // Define the hierarchical layout with proper spacing and alignment
        // Each level is properly spaced and nodes within levels are evenly distributed
        const demoPositions = {
            // Root node - centered vertically in the layout
            'Enhanced File Reader': {
                x: layoutConfig.columns.root,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 3) // Centered among 4 level1 nodes
            },

            // Level 1 - Direct dependencies of root, evenly spaced vertically
            'Streaming Processor': {
                x: layoutConfig.columns.level1,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 1.5)
            },
            'Size Validation': {
                x: layoutConfig.columns.level1,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 2.5)
            },
            'Permission Checks': {
                x: layoutConfig.columns.level1,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 3.5)
            },
            'Timeout Protection': {
                x: layoutConfig.columns.level1,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 4.5)
            },

            // Level 2 - Second tier dependencies, aligned and spaced properly
            'Resilient Worker Pool': {
                x: layoutConfig.columns.level2,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 1)
            },
            'Chunked Processing': {
                x: layoutConfig.columns.level2,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 2.5)
            },
            'Backpressure Control': {
                x: layoutConfig.columns.level2,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 4)
            },

            // Level 3 - Third tier dependencies, properly aligned
            'Memory-Safe Parser': {
                x: layoutConfig.columns.level3,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 0.5)
            },
            'Worker Health Monitor': {
                x: layoutConfig.columns.level3,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 1.8)
            },
            'Graceful Termination': {
                x: layoutConfig.columns.level3,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 3.1)
            },
            'Error Recovery': {
                x: layoutConfig.columns.level3,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 4.4)
            },

            // Level 4 - Final tier, evenly distributed
            'Progressive Results': {
                x: layoutConfig.columns.level4,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 0)
            },
            'Incremental Aggregator': {
                x: layoutConfig.columns.level4,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 1)
            },
            'Memory Cleanup': {
                x: layoutConfig.columns.level4,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 2)
            },
            'Regex Timeouts': {
                x: layoutConfig.columns.level4,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 3)
            },
            'Memory Limits': {
                x: layoutConfig.columns.level4,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 4)
            },
            'Corruption Detection': {
                x: layoutConfig.columns.level4,
                y: layoutConfig.rows.baseY + (layoutConfig.rows.spacing * 5)
            }
        };

        // Apply positions based on node names/filenames
        nodes.forEach(node => {
            // Try to match by text first, then by filename patterns
            let position = null;

            // Direct text match
            if (demoPositions[node.text]) {
                position = demoPositions[node.text];
            } else {
                // Pattern matching for common variations
                const nodeText = node.text.toLowerCase();
                const filename = node.filename.toLowerCase();

                if ((nodeText.includes('enhanced') && nodeText.includes('reader')) ||
                    (filename.includes('enhanced') && filename.includes('reader'))) {
                    position = demoPositions['Enhanced File Reader'];
                } else if (nodeText.includes('streaming') || nodeText.includes('processor') || filename.includes('stream')) {
                    position = demoPositions['Streaming Processor'];
                } else if (nodeText.includes('size') || nodeText.includes('validation') || filename.includes('validate')) {
                    position = demoPositions['Size Validation'];
                } else if (nodeText.includes('permission') || nodeText.includes('checks') || filename.includes('auth')) {
                    position = demoPositions['Permission Checks'];
                } else if (nodeText.includes('timeout') || nodeText.includes('protection') || filename.includes('timeout')) {
                    position = demoPositions['Timeout Protection'];
                } else if (nodeText.includes('resilient') || nodeText.includes('worker') || nodeText.includes('pool') || filename.includes('workers')) {
                    position = demoPositions['Resilient Worker Pool'];
                } else if (nodeText.includes('chunked') || nodeText.includes('processing') || filename.includes('chunks')) {
                    position = demoPositions['Chunked Processing'];
                } else if (nodeText.includes('backpressure') || nodeText.includes('control') || filename.includes('pressure')) {
                    position = demoPositions['Backpressure Control'];
                } else if (nodeText.includes('memory') && nodeText.includes('safe') || nodeText.includes('parser') || filename.includes('parser')) {
                    position = demoPositions['Memory-Safe Parser'];
                } else if (nodeText.includes('worker') && nodeText.includes('health') || nodeText.includes('monitor') || filename.includes('health')) {
                    position = demoPositions['Worker Health Monitor'];
                } else if (nodeText.includes('graceful') || nodeText.includes('termination') || filename.includes('shutdown')) {
                    position = demoPositions['Graceful Termination'];
                } else if (nodeText.includes('error') || nodeText.includes('recovery') || filename.includes('errors')) {
                    position = demoPositions['Error Recovery'];
                } else if (nodeText.includes('progressive') || nodeText.includes('results') || filename.includes('results')) {
                    position = demoPositions['Progressive Results'];
                } else if (nodeText.includes('incremental') || nodeText.includes('aggregator') || filename.includes('aggregate')) {
                    position = demoPositions['Incremental Aggregator'];
                } else if (nodeText.includes('memory') && nodeText.includes('cleanup') || filename.includes('cleanup')) {
                    position = demoPositions['Memory Cleanup'];
                } else if (nodeText.includes('regex') || nodeText.includes('timeouts') || filename.includes('regex')) {
                    position = demoPositions['Regex Timeouts'];
                } else if (nodeText.includes('memory') && nodeText.includes('limits') || filename.includes('limits')) {
                    position = demoPositions['Memory Limits'];
                } else if (nodeText.includes('corruption') || nodeText.includes('detection') || filename.includes('detect')) {
                    position = demoPositions['Corruption Detection'];
                }
            }

            if (position) {
                node.x = position.x;
                node.y = position.y;
            } else {
                // Improved fallback positioning for unmatched nodes
                // Each file type gets its own independent positioning to prevent overlaps
                const nodeType = (node.type || '').toLowerCase();

                // Count nodes of the same type that have already been positioned
                const sameTypeNodes = nodes.filter((n, i) => {
                    if (i >= nodes.indexOf(node)) return false; // Only count nodes processed before this one
                    const nType = (n.type || '').toLowerCase();
                    if (nodeType === 'html' || nodeType === 'htm') {
                        return nType === 'html' || nType === 'htm';
                    } else if (nodeType === 'css' || nodeType === 'scss' || nodeType === 'sass' || nodeType === 'less') {
                        return nType === 'css' || nType === 'scss' || nType === 'sass' || nType === 'less';
                    } else if (nodeType === 'js' || nodeType === 'mjs' || nodeType === 'jsx' || nodeType === 'ts' || nodeType === 'tsx') {
                        return nType === 'js' || nType === 'mjs' || nType === 'jsx' || nType === 'ts' || nType === 'tsx';
                    } else {
                        return nType === nodeType;
                    }
                });
                const typeIndex = sameTypeNodes.length;

                // Position each file type in its own area to prevent overlaps
                if (nodeType === 'html' || nodeType === 'htm') {
                    // HTML files: Top-left area
                    node.x = 50 + (typeIndex % 3) * 180;
                    node.y = 50 + Math.floor(typeIndex / 3) * 70;
                } else if (nodeType === 'css' || nodeType === 'scss' || nodeType === 'sass' || nodeType === 'less') {
                    // CSS files: Top-center area
                    node.x = 350 + (typeIndex % 3) * 180;
                    node.y = 50 + Math.floor(typeIndex / 3) * 70;
                } else if (nodeType === 'js' || nodeType === 'mjs' || nodeType === 'jsx' || nodeType === 'ts' || nodeType === 'tsx') {
                    // JS/TS files: Top-right area
                    node.x = 650 + (typeIndex % 3) * 180;
                    node.y = 50 + Math.floor(typeIndex / 3) * 70;
                } else {
                    // Other file types: Bottom area
                    node.x = 50 + (typeIndex % 4) * 200;
                    node.y = 300 + Math.floor(typeIndex / 4) * 80;
                }
            }
        });
    }

    /**
     * Get export count for a node
     * @param {Object} node - Node data from scanner
     * @returns {number} - Number of exports
     */
    getExportCount(node) {
        if (node.exports && Array.isArray(node.exports)) {
            return node.exports.length;
        }
        if (node.exportsCount && typeof node.exportsCount === 'number') {
            return node.exportsCount;
        }
        // Default heuristic: if it has export capability, assume 1 export
        return this.determineHasExport(node) ? 1 : 0;
    }



    /**
     * Extract filename from path
     * @param {string} path - File path
     * @returns {string} - Filename
     */
    extractFilename(path) {
        if (!path) return 'unknown';
        return path.split('/').pop() || path.split('\\').pop() || path;
    }

    /**
     * Calculate node width based on text length
     * @param {string} text - Node text
     * @returns {number} - Width in pixels
     */
    calculateNodeWidth(text) {
        const baseWidth = 80;
        const charWidth = 8;
        return Math.max(baseWidth, Math.min(200, text.length * charWidth));
    }

    /**
     * Determine if node has import capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasImport(node) {
        // Check actual scanner data structure
        return !!(
            (node.imports && node.imports.length > 0) ||
            (node.importsCount && node.importsCount > 0) ||
            (node.category && ['entry', 'component', 'service'].includes(node.category)) ||
            (node.type && ['js', 'jsx', 'ts', 'tsx', 'css', 'scss', 'sass', 'less', 'html', 'htm'].includes(node.type))
        );
    }

    /**
     * Determine if node has export capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasExport(node) {
        // Check actual scanner data structure
        return !!(
            (node.exports && node.exports.length > 0) ||
            (node.exportsCount && node.exportsCount > 0) ||
            (node.category && ['component', 'service', 'utility'].includes(node.category)) ||
            (node.type && ['js', 'jsx', 'ts', 'tsx', 'css', 'scss', 'sass', 'less', 'html', 'htm'].includes(node.type))
        );
    }

    /**
     * Determine if node has event capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasEvent(node) {
        // Check actual scanner data structure
        return !!(
            (node.events && node.events.length > 0) ||
            (node.eventsCount && node.eventsCount > 0) ||
            node.hasEvents ||
            (node.category && ['component', 'service'].includes(node.category)) ||
            (node.functionsCount && node.functionsCount > 0)
        );
    }

    /**
     * Determine if node has worker capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasWorker(node) {
        // Check actual scanner data structure
        return !!(
            (node.category && ['service', 'utility'].includes(node.category)) ||
            (node.isComplex && node.complexity > 5) ||
            (node.type && ['js', 'ts'].includes(node.type)) ||
            (node.functionsCount && node.functionsCount > 3) ||
            (node.classesCount && node.classesCount > 0)
        );
    }

    /**
     * Get node colors based on file type for visual differentiation
     * @param {Object} node - Node data
     * @returns {Object} - Object containing fill and stroke colors
     */
    getNodeColors(node) {
        // Extract file extension from filename or path
        const filename = node.filename || node.name || node.path || '';
        const extension = filename.split('.').pop()?.toLowerCase() || '';



        // Define color scheme for different file types
        const colorSchemes = {
            // HTML files - bright orange tones
            'html': {
                fill: '#374151',
                stroke: '#fb923c'
            },
            'htm': {
                fill: '#374151',
                stroke: '#fb923c'
            },
            // CSS files - bright purple tones
            'css': {
                fill: '#374151',
                stroke: '#c084fc'
            },
            'scss': {
                fill: '#374151',
                stroke: '#c084fc'
            },
            'sass': {
                fill: '#374151',
                stroke: '#c084fc'
            },
            'less': {
                fill: '#374151',
                stroke: '#c084fc'
            },
            // JavaScript files - default gray (keep current appearance)
            'js': {
                fill: '#374151',
                stroke: '#6b7280'
            },
            'jsx': {
                fill: '#374151',
                stroke: '#6b7280'
            },
            // ES6 Module files - bright green
            'mjs': {
                fill: '#374151',
                stroke: '#10b981'
            },
            // TypeScript files - bright blue
            'ts': {
                fill: '#374151',
                stroke: '#3b82f6'
            },
            'tsx': {
                fill: '#374151',
                stroke: '#3b82f6'
            },
            // JSON files - bright yellow
            'json': {
                fill: '#374151',
                stroke: '#f59e0b'
            },
            'vue': {
                fill: '#374151',
                stroke: '#6b7280'
            },
            'svelte': {
                fill: '#374151',
                stroke: '#6b7280'
            },
            // Default for unknown file types
            'default': {
                fill: '#374151',
                stroke: '#6b7280'
            }
        };

        const colors = colorSchemes[extension] || colorSchemes.default;



        return colors;
    }

    /**
     * Creates the DOM elements for a mind map container.
     * @returns {DocumentFragment} A fragment containing the style and container elements, ready to be appended to the DOM.
     */
    createMindmapElement() {
        // A DocumentFragment is a lightweight container to hold nodes before appending them to the main DOM.
        const fragment = document.createDocumentFragment();

        // 1. Create the <style> element
        const style = document.createElement('style');
        style.textContent = `
            .container { max-width: 1200px; margin: 0 auto; }
            .mindmap-container { background: #1f2937; border-radius: 8px; padding: 24px; }
            svg { width: 100%; height: auto; }
        `;

        // 2. Create the main container div
        const containerDiv = document.createElement('div');
        containerDiv.className = 'container';

        // 3. Create the mindmap container div
        const mindmapContainerDiv = document.createElement('div');
        mindmapContainerDiv.className = 'mindmap-container';

        // 4. Create the SVG element
        // IMPORTANT: SVG elements must be created with a namespace
        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.createElementNS(svgNS, 'svg');
        svg.setAttribute('width', '1100');
        svg.setAttribute('height', '500');
        svg.setAttribute('viewBox', `${this.viewBox.x} ${this.viewBox.y} ${this.viewBox.width} ${this.viewBox.height}`);
        svg.setAttribute('id', 'mindmap-svg');

        // Add zoom and pan event listeners
        this.setupZoomAndPan(svg);

        // 5. Create zoom controls
        const zoomControls = this.createZoomControls();

        // 6. Assemble the elements like building blocks
        mindmapContainerDiv.appendChild(svg);
        mindmapContainerDiv.appendChild(zoomControls);
        containerDiv.appendChild(mindmapContainerDiv);

        // 6. Add the style and the main container to the fragment
        fragment.appendChild(style);
        fragment.appendChild(containerDiv);

        return fragment;
    }

    // Calculate connection paths
    getConnectionPath(from, to) {
        const fromNode = this.nodes.find((n) => n.id === from);
        const toNode = this.nodes.find((n) => n.id === to);

        const fromX = fromNode.x + fromNode.width;
        const fromY = fromNode.y + fromNode.height / 2;
        const toX = toNode.x;
        const toY = toNode.y + toNode.height / 2;

        // Create curved path
        const midX = fromX + (toX - fromX) * 0.5;
        const controlX1 = fromX + (midX - fromX) * 0.7;
        const controlX2 = toX - (toX - midX) * 0.7;

        return `M ${fromX} ${fromY} C ${controlX1} ${fromY}, ${controlX2} ${toY}, ${toX} ${toY}`;
    }

    // Calculate special connection paths for worker/event flows
    getSpecialConnectionPath(from, to, type) {
        const fromNode = this.nodes.find((n) => n.id === from);
        const toNode = this.nodes.find((n) => n.id === to);

        let fromX, fromY, toX, toY;

        if (type === 'worker') {
            // Worker connections: from top blue circle to top blue circle
            fromX = fromNode.x + fromNode.width / 2;
            fromY = fromNode.y - 8;
            toX = toNode.x + toNode.width / 2;
            toY = toNode.y - 8;
        } else if (type === 'event') {
            // Event connections: from bottom yellow circle to bottom yellow circle
            fromX = fromNode.x + fromNode.width / 2;
            fromY = fromNode.y + fromNode.height + 8;
            toX = toNode.x + toNode.width / 2;
            toY = toNode.y + toNode.height + 8;
        }

        // Create curved path
        const midX = fromX + (toX - fromX) * 0.5;
        const controlX1 = fromX + (midX - fromX) * 0.7;
        const controlX2 = toX - (toX - midX) * 0.7;

        return {
            path: `M ${fromX} ${fromY} C ${controlX1} ${fromY}, ${controlX2} ${toY}, ${toX} ${toY}`,
            endX: toX,
            endY: toY,
        };
    }

    setSvgElement(svgElement) {
        this.svgElement = svgElement;
    }

    handleMouseDown(e, nodeId) {
        e.preventDefault();
        if (!this.svgElement) return;

        const svgRect = this.svgElement.getBoundingClientRect();

        // Convert mouse position to SVG coordinates using current viewBox
        const svgPoint = {
            x: this.viewBox.x + (e.clientX - svgRect.left) * (this.viewBox.width / svgRect.width),
            y: this.viewBox.y + (e.clientY - svgRect.top) * (this.viewBox.height / svgRect.height),
        };

        const node = this.nodes.find((n) => n.id === nodeId);
        this.dragState = {
            isDragging: true,
            draggedNodeId: nodeId,
            offset: {
                x: svgPoint.x - node.x,
                y: svgPoint.y - node.y,
            },
        };

        this.addEventListeners();
    }

    handleMouseMove(e) {
        if (!this.dragState.isDragging || !this.dragState.draggedNodeId || !this.svgElement) return;

        e.preventDefault();
        const svgRect = this.svgElement.getBoundingClientRect();

        // Convert mouse position to SVG coordinates using current viewBox
        const svgPoint = {
            x: this.viewBox.x + (e.clientX - svgRect.left) * (this.viewBox.width / svgRect.width),
            y: this.viewBox.y + (e.clientY - svgRect.top) * (this.viewBox.height / svgRect.height),
        };

        // Update node position without boundary constraints
        this.nodes = this.nodes.map((node) =>
            node.id === this.dragState.draggedNodeId
                ? {
                      ...node,
                      x: svgPoint.x - this.dragState.offset.x,
                      y: svgPoint.y - this.dragState.offset.y,
                  }
                : node,
        );

        // Trigger re-render
        this.render();
    }

    handleMouseUp() {
        this.dragState = {
            isDragging: false,
            draggedNodeId: null,
            offset: { x: 0, y: 0 },
        };

        this.removeEventListeners();
    }
    
    /**
     * Handle node click for focus mode
     * @param {MouseEvent} e - Mouse event
     * @param {string} nodeId - ID of the clicked node
     */
    handleNodeClick(e, nodeId) {
        // Always prevent default browser behavior
        e.preventDefault();

        // If we're already in drag operation, don't trigger selection
        if (this.dragState.isDragging) {
            return;
        }

        // Toggle selection - if clicking the same node, clear selection
        if (this.selectedNodeId === nodeId) {
            this.selectedNodeId = null;

            // Hide all lines when node is deselected
            if (this.lineVisibilityController) {
                this.lineVisibilityController.hideAllLinesForNodeSelection();
            }
        } else {
            this.selectedNodeId = nodeId;
            // Note: Lines are already shown in the click handler above
        }

        // Re-render to apply focus effect
        this.render();

        // Stop propagation to prevent other handlers
        e.stopPropagation();
        return false; // Explicitly stop event chain
    }

    addEventListeners() {
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mouseup', this.handleMouseUp);
    }

    removeEventListeners() {
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mouseup', this.handleMouseUp);
    }

    /**
     * Setup zoom and pan functionality for the SVG element
     * @param {SVGElement} svg - The SVG element to add zoom and pan to
     */
    setupZoomAndPan(svg) {
        // Remove any existing event listeners first
        svg.removeEventListener('mousedown', this.handlePanStart);
        svg.removeEventListener('wheel', this.handleWheel);

        // Add event listeners with capture to ensure they fire first
        svg.addEventListener('mousedown', this.handlePanStart, true);
        svg.addEventListener('wheel', this.handleWheel, { passive: false, capture: true });

        // Prevent context menu on right click
        svg.addEventListener('contextmenu', (e) => e.preventDefault());

        // Set cursor style
        svg.style.cursor = 'grab';

        // Add a data attribute to identify this as a mindmap SVG
        svg.setAttribute('data-mindmap-zoom-pan', 'true');
    }

    /**
     * Handle pan start (mouse down on SVG background)
     * @param {MouseEvent} e - Mouse event
     */
    handlePanStart(e) {
        // Only pan on left mouse button and not on nodes
        if (e.button !== 0 || e.target.closest('.node')) {
            return;
        }

        e.preventDefault();
        e.stopPropagation(); // Prevent other event handlers from interfering

        this.isPanning = true;
        this.panStartPoint = { x: e.clientX, y: e.clientY };

        // Add pan move and end listeners
        document.addEventListener('mousemove', this.handlePanMove);
        document.addEventListener('mouseup', this.handlePanEnd);

        // Change cursor
        if (this.svgElement) {
            this.svgElement.style.cursor = 'grabbing';
        }
    }

    /**
     * Handle pan move (mouse move while panning)
     * @param {MouseEvent} e - Mouse event
     */
    handlePanMove(e) {
        if (!this.isPanning || !this.svgElement) return;

        e.preventDefault();

        // Cache SVG dimensions to avoid repeated getBoundingClientRect calls
        if (!this.cachedSvgRect) {
            this.cachedSvgRect = this.svgElement.getBoundingClientRect();
        }

        // Calculate pan delta
        const deltaX = (this.panStartPoint.x - e.clientX) * (this.viewBox.width / this.cachedSvgRect.width);
        const deltaY = (this.panStartPoint.y - e.clientY) * (this.viewBox.height / this.cachedSvgRect.height);

        // Update viewBox
        this.viewBox.x += deltaX;
        this.viewBox.y += deltaY;

        // Update pan start point for next move
        this.panStartPoint = { x: e.clientX, y: e.clientY };

        // Apply the new viewBox (but skip expensive minimap updates during pan)
        this.updateViewBox();

        // Use requestAnimationFrame to throttle minimap updates
        if (!this.minimapUpdatePending) {
            this.minimapUpdatePending = true;
            requestAnimationFrame(() => {
                this.updateMinimap();
                this.minimapUpdatePending = false;
            });
        }
    }

    /**
     * Handle pan end (mouse up)
     * @param {MouseEvent} e - Mouse event
     */
    handlePanEnd(e) {
        this.isPanning = false;

        // Clear cached SVG rect
        this.cachedSvgRect = null;

        // Remove pan listeners
        document.removeEventListener('mousemove', this.handlePanMove);
        document.removeEventListener('mouseup', this.handlePanEnd);

        // Reset cursor
        if (this.svgElement) {
            this.svgElement.style.cursor = 'grab';
        }

        // Ensure final minimap update
        this.updateMinimap();
    }

    /**
     * Handle mouse wheel for zooming
     * @param {WheelEvent} e - Wheel event
     */
    handleWheel(e) {
        e.preventDefault();
        e.stopPropagation(); // Prevent other event handlers from interfering

        const zoomIntensity = 0.1;
        const zoomFactor = e.deltaY < 0 ? (1 - zoomIntensity) : (1 + zoomIntensity);

        // Get the SVG element from the event target
        const svgElement = e.currentTarget;
        if (!svgElement) {
            console.error('SVG element not found in wheel event');
            return;
        }

        // Get mouse position relative to SVG
        const svgRect = svgElement.getBoundingClientRect();
        const mouseX = e.clientX - svgRect.left;
        const mouseY = e.clientY - svgRect.top;

        // Convert mouse position to SVG coordinates
        const svgMouseX = this.viewBox.x + (mouseX / svgRect.width) * this.viewBox.width;
        const svgMouseY = this.viewBox.y + (mouseY / svgRect.height) * this.viewBox.height;

        // Calculate new viewBox dimensions
        const newWidth = this.viewBox.width * zoomFactor;
        const newHeight = this.viewBox.height * zoomFactor;

        // Check zoom limits
        const newZoomLevel = 1000 / newWidth; // Base width is 1000
        if (newZoomLevel < this.minZoom || newZoomLevel > this.maxZoom) {
            return; // Don't zoom if it would exceed limits
        }

        // Calculate new viewBox position to zoom towards mouse
        const newX = svgMouseX - (mouseX / svgRect.width) * newWidth;
        const newY = svgMouseY - (mouseY / svgRect.height) * newHeight;

        // Update viewBox
        this.viewBox.x = newX;
        this.viewBox.y = newY;
        this.viewBox.width = newWidth;
        this.viewBox.height = newHeight;
        this.zoomLevel = newZoomLevel;

        // Apply the new viewBox
        this.updateViewBox();

        // Use requestAnimationFrame to throttle minimap updates during zoom
        if (!this.minimapUpdatePending) {
            this.minimapUpdatePending = true;
            requestAnimationFrame(() => {
                this.updateMinimap();
                this.minimapUpdatePending = false;
            });
        }
    }

    /**
     * Update the SVG viewBox attribute
     */
    updateViewBox() {
        if (this.svgElement) {
            const viewBoxString = `${this.viewBox.x} ${this.viewBox.y} ${this.viewBox.width} ${this.viewBox.height}`;
            this.svgElement.setAttribute('viewBox', viewBoxString);
        }
    }

    /**
     * Reset zoom and pan to default view
     */
    resetView() {
        this.viewBox = { x: 0, y: 0, width: 1000, height: 550 };
        this.zoomLevel = 1;
        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Check if we should zoom to fit all nodes
     * @returns {boolean} - True if zoom to fit is needed
     */
    shouldZoomToFit() {
        // Only zoom to fit if this is the first render or if there are nodes outside the viewport
        if (this.nodes.length === 0) return false;

        // Check if any nodes are outside the current viewport
        const hasNodesOutsideViewport = this.nodes.some(node => {
            return node.x < this.viewBox.x ||
                   node.y < this.viewBox.y ||
                   node.x + node.width > this.viewBox.x + this.viewBox.width ||
                   node.y + node.height > this.viewBox.y + this.viewBox.height;
        });

        return hasNodesOutsideViewport;
    }

    /**
     * Zoom to fit all nodes
     */
    zoomToFit() {
        if (this.nodes.length === 0) {
            this.resetView();
            return;
        }

        // Calculate bounding box of all nodes
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

        this.nodes.forEach(node => {
            minX = Math.min(minX, node.x);
            minY = Math.min(minY, node.y);
            maxX = Math.max(maxX, node.x + node.width);
            maxY = Math.max(maxY, node.y + node.height);
        });

        // Add padding
        const padding = 50;
        minX -= padding;
        minY -= padding;
        maxX += padding;
        maxY += padding;

        // Calculate new viewBox
        const width = maxX - minX;
        const height = maxY - minY;

        this.viewBox = { x: minX, y: minY, width, height };
        this.zoomLevel = 1000 / width; // Base width is 1000

        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Add zoom controls to the container of an external SVG element
     */
    addZoomControlsToContainer() {
        if (!this.svgElement || !this.svgElement.parentElement) return;

        const container = this.svgElement.parentElement;

        // Make sure the container has relative positioning
        const containerStyle = window.getComputedStyle(container);
        if (containerStyle.position === 'static') {
            container.style.position = 'relative';
        }

        // Create and add zoom controls
        const zoomControls = this.createZoomControls();
        container.appendChild(zoomControls);


    }

    /**
     * Create zoom control buttons
     * @returns {HTMLElement} - Zoom controls container
     */
    createZoomControls() {
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'zoom-controls';
        controlsContainer.style.cssText = `
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 1000;
            pointer-events: auto;
        `;

        // Zoom In button
        const zoomInBtn = document.createElement('button');
        zoomInBtn.innerHTML = '+';
        zoomInBtn.title = 'Zoom In';
        zoomInBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        zoomInBtn.addEventListener('click', () => this.zoomIn());

        // Zoom Out button
        const zoomOutBtn = document.createElement('button');
        zoomOutBtn.innerHTML = '−';
        zoomOutBtn.title = 'Zoom Out';
        zoomOutBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        zoomOutBtn.addEventListener('click', () => this.zoomOut());

        // Reset View button
        const resetBtn = document.createElement('button');
        resetBtn.innerHTML = '⌂';
        resetBtn.title = 'Reset View';
        resetBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        resetBtn.addEventListener('click', () => this.resetView());

        // Fit to View button
        const fitBtn = document.createElement('button');
        fitBtn.innerHTML = '⊞';
        fitBtn.title = 'Fit to View';
        fitBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        fitBtn.addEventListener('click', () => this.zoomToFit());

        // Add hover effects
        [zoomInBtn, zoomOutBtn, resetBtn, fitBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.background = 'rgba(0, 0, 0, 0.9)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.background = 'rgba(0, 0, 0, 0.7)';
            });
        });

        controlsContainer.appendChild(zoomInBtn);
        controlsContainer.appendChild(zoomOutBtn);
        controlsContainer.appendChild(resetBtn);
        controlsContainer.appendChild(fitBtn);

        return controlsContainer;
    }

    /**
     * Zoom in by a fixed amount
     */
    zoomIn() {
        const zoomFactor = 0.8; // Zoom in by 20%
        const centerX = this.viewBox.x + this.viewBox.width / 2;
        const centerY = this.viewBox.y + this.viewBox.height / 2;

        const newWidth = this.viewBox.width * zoomFactor;
        const newHeight = this.viewBox.height * zoomFactor;

        // Check zoom limits
        const newZoomLevel = 1000 / newWidth;
        if (newZoomLevel > this.maxZoom) return;

        this.viewBox.x = centerX - newWidth / 2;
        this.viewBox.y = centerY - newHeight / 2;
        this.viewBox.width = newWidth;
        this.viewBox.height = newHeight;
        this.zoomLevel = newZoomLevel;

        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Zoom out by a fixed amount
     */
    zoomOut() {
        const zoomFactor = 1.25; // Zoom out by 25%
        const centerX = this.viewBox.x + this.viewBox.width / 2;
        const centerY = this.viewBox.y + this.viewBox.height / 2;

        const newWidth = this.viewBox.width * zoomFactor;
        const newHeight = this.viewBox.height * zoomFactor;

        // Check zoom limits
        const newZoomLevel = 1000 / newWidth;
        if (newZoomLevel < this.minZoom) return;

        this.viewBox.x = centerX - newWidth / 2;
        this.viewBox.y = centerY - newHeight / 2;
        this.viewBox.width = newWidth;
        this.viewBox.height = newHeight;
        this.zoomLevel = newZoomLevel;

        this.updateViewBox();
        this.updateMinimap();
    }

    render() {
        if (!this.svgElement) {
            console.warn('MindmapProject.render - No SVG element available');
            return;
        }

        // Render starting - removed console.log for performance

        // Clear existing content
        this.svgElement.innerHTML = '';
        
        // Create group elements for proper layering
        const backgroundLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        const connectionsLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        const nodesLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        
        // Append layers in correct order (back to front)
        this.svgElement.appendChild(backgroundLayer);
        this.svgElement.appendChild(connectionsLayer);
        this.svgElement.appendChild(nodesLayer);
        
        // Add background rectangle to handle clearing selection
        const background = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        background.setAttribute('width', '100%');
        background.setAttribute('height', '100%');
        background.setAttribute('fill', 'transparent');
        background.addEventListener('mousedown', (e) => {
            // Only handle direct background clicks (not bubbled events)
            if (e.target === background && this.selectedNodeId) {

                this.selectedNodeId = null;

                // Hide all lines when background is clicked
                if (this.lineVisibilityController) {
                    this.lineVisibilityController.hideAllLinesForNodeSelection();
                }

                this.render();
            }
        });
        backgroundLayer.appendChild(background);

        // Create gradients
        this.createGradients();
        
        // Render connections on the connections layer
        this.renderConnections(connectionsLayer);
        this.renderWorkerConnections(connectionsLayer);
        this.renderEventConnections(connectionsLayer);
        
        // Render nodes on the nodes layer
        this.renderNodes(nodesLayer);
        
        // Render arrows on the connections layer
        this.renderArrows(connectionsLayer);
        
        // Create or update minimap
        this.createMinimap();
        this.renderMinimap();
        
        // Render complete - removed console.log for performance

        // Update line visibility controller after render
        if (this.lineVisibilityController) {
            setTimeout(() => {
                this.lineVisibilityController.applyAllVisibilityStates();
            }, 50);
        }
    }

    createGradients() {
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

        // Worker gradient
        const workerGradient = document.createElementNS(
            'http://www.w3.org/2000/svg',
            'linearGradient',
        );
        workerGradient.setAttribute('id', 'workerGradient');
        workerGradient.setAttribute('x1', '0%');
        workerGradient.setAttribute('y1', '0%');
        workerGradient.setAttribute('x2', '100%');
        workerGradient.setAttribute('y2', '0%');

        const workerStops = [
            { offset: '0%', color: '#3b82f6', opacity: '1' },
            { offset: '50%', color: '#3b82f6', opacity: '0.2' },
            { offset: '100%', color: '#3b82f6', opacity: '1' },
        ];

        workerStops.forEach((stop) => {
            const stopElement = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
            stopElement.setAttribute('offset', stop.offset);
            stopElement.setAttribute('stop-color', stop.color);
            stopElement.setAttribute('stop-opacity', stop.opacity);
            workerGradient.appendChild(stopElement);
        });

        // Event gradient
        const eventGradient = document.createElementNS(
            'http://www.w3.org/2000/svg',
            'linearGradient',
        );
        eventGradient.setAttribute('id', 'eventGradient');
        eventGradient.setAttribute('x1', '0%');
        eventGradient.setAttribute('y1', '0%');
        eventGradient.setAttribute('x2', '100%');
        eventGradient.setAttribute('y2', '0%');

        const eventStops = [
            { offset: '0%', color: '#eab308', opacity: '1' },
            { offset: '50%', color: '#eab308', opacity: '0.2' },
            { offset: '100%', color: '#eab308', opacity: '1' },
        ];

        eventStops.forEach((stop) => {
            const stopElement = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
            stopElement.setAttribute('offset', stop.offset);
            stopElement.setAttribute('stop-color', stop.color);
            stopElement.setAttribute('stop-opacity', stop.opacity);
            eventGradient.appendChild(stopElement);
        });

        defs.appendChild(workerGradient);
        defs.appendChild(eventGradient);
        this.svgElement.appendChild(defs);
    }

    renderConnections(parentElement = this.svgElement) {
        this.connections.forEach((conn, index) => {
            const fromNode = this.nodes.find((n) => n.id === conn.from);
            const toNode = this.nodes.find((n) => n.id === conn.to);
            
            if (!fromNode || !toNode) {
                console.warn('MindmapProject.renderConnections - Missing nodes for connection', index, ':', conn, 'fromNode:', !!fromNode, 'toNode:', !!toNode);
                return;
            }
            
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', this.getConnectionPath(conn.from, conn.to));
            path.setAttribute('stroke', '#9ca3af'); // Lighter gray for better visibility
            path.setAttribute('stroke-width', '2'); // Increased from 1.5
            path.setAttribute('fill', 'none');
            path.setAttribute('stroke-dasharray', '4,4'); // Slightly larger dashes
            
            // Apply focus mode - if a node is selected, make non-connected paths slightly dimmed
            if (this.selectedNodeId && conn.from !== this.selectedNodeId && conn.to !== this.selectedNodeId) {
                path.setAttribute('opacity', '0.4'); // Dimmed but still visible when not connected to selected node
                path.setAttribute('stroke-width', '1.5'); // Standard stroke for non-connected paths
            } else if (this.selectedNodeId) {
                path.setAttribute('opacity', '1'); // Full visibility for connected paths
                path.setAttribute('stroke-width', '3'); // Slightly thicker stroke for connected paths
                // Keep original stroke color but make it slightly brighter
                path.setAttribute('stroke', '#a5b3c5');
                
                // Add a data attribute to identify which node this connection belongs to
            }
            
            parentElement.appendChild(path);
        });
        // Connections rendered - removed console.log for performance
    }

    renderWorkerConnections(parentElement = this.svgElement) {
        this.workerConnections.forEach((conn, index) => {
            const fromNode = this.nodes.find((n) => n.id === conn.from);
            const toNode = this.nodes.find((n) => n.id === conn.to);
            
            if (!fromNode || !toNode) {
                console.warn('MindmapProject.renderWorkerConnections - Missing nodes for worker connection', index, ':', conn);
                return;
            }
            
            const { path, endX, endY } = this.getSpecialConnectionPath(
                conn.from,
                conn.to,
                'worker',
            );

            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            
            // Apply focus mode - maintain visibility but highlight relevant connections
            if (this.selectedNodeId && conn.from !== this.selectedNodeId && conn.to !== this.selectedNodeId) {
                g.setAttribute('opacity', '0.4'); // Dimmed but still visible
            } else if (this.selectedNodeId) {
                g.setAttribute('opacity', '1'); // Full visibility for connected paths
                
                // Add a data attribute to identify which node this connection belongs to
                if (conn.from === this.selectedNodeId) {
                    g.setAttribute('data-connected-to-selected', 'from');
                } else if (conn.to === this.selectedNodeId) {
                    g.setAttribute('data-connected-to-selected', 'to');
                }
            }

            const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathElement.setAttribute('d', path);
            pathElement.setAttribute('stroke', 'url(#workerGradient)');
            pathElement.setAttribute('stroke-width', '3'); // Increased from 2
            pathElement.setAttribute('fill', 'none');
            pathElement.setAttribute('stroke-dasharray', '5,5'); // Larger dashes

            const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrow.setAttribute(
                'points',
                `${endX - 6},${endY - 3} ${endX + 2},${endY} ${endX - 6},${endY + 3}`,
            );
            arrow.setAttribute('fill', '#3b82f6');

            g.appendChild(pathElement);
            g.appendChild(arrow);
            parentElement.appendChild(g);
        });

    }

    renderEventConnections(parentElement = this.svgElement) {
        this.eventConnections.forEach((conn, index) => {
            const fromNode = this.nodes.find((n) => n.id === conn.from);
            const toNode = this.nodes.find((n) => n.id === conn.to);
            
            if (!fromNode || !toNode) {
                console.warn('MindmapProject.renderEventConnections - Missing nodes for event connection', index, ':', conn);
                return;
            }
            
            const { path, endX, endY } = this.getSpecialConnectionPath(conn.from, conn.to, 'event');

            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            
            // Apply focus mode - maintain visibility but highlight relevant connections
            if (this.selectedNodeId && conn.from !== this.selectedNodeId && conn.to !== this.selectedNodeId) {
                g.setAttribute('opacity', '0.4'); // Dimmed but still visible
            } else if (this.selectedNodeId) {
                g.setAttribute('opacity', '1'); // Full visibility for connected paths
                
                // Add a data attribute to identify which node this connection belongs to
                if (conn.from === this.selectedNodeId) {
                    g.setAttribute('data-connected-to-selected', 'from');
                } else if (conn.to === this.selectedNodeId) {
                    g.setAttribute('data-connected-to-selected', 'to');
                }
            }

            const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathElement.setAttribute('d', path);
            pathElement.setAttribute('stroke', 'url(#eventGradient)');
            pathElement.setAttribute('stroke-width', '2.5'); // Adjusted for better visibility
            pathElement.setAttribute('fill', 'none');
            pathElement.setAttribute('stroke-dasharray', '2,3'); // Adjusted for event styling

            const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrow.setAttribute(
                'points',
                `${endX - 6},${endY - 3} ${endX + 2},${endY} ${endX - 6},${endY + 3}`,
            );
            arrow.setAttribute('fill', '#ec4899'); // Pink for events

            g.appendChild(pathElement);
            g.appendChild(arrow);
            parentElement.appendChild(g);
        });

    }

    renderNodes(parentElement = this.svgElement) {

        this.nodes.forEach((node) => {
            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            g.setAttribute('class', 'node');
            g.setAttribute('data-id', node.id);
            
            // Apply selected state styling if this node is selected
            if (node.id === this.selectedNodeId) {
                g.classList.add('selected-node');
                // Add a subtle highlight effect for selected nodes
                const highlight = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                highlight.setAttribute('x', node.x - (node.width/2) - 3);
                highlight.setAttribute('y', node.y - (node.height/2) - 3);
                highlight.setAttribute('width', node.width + 6);
                highlight.setAttribute('height', node.height + 6);
                highlight.setAttribute('rx', '8'); // Rounded corners
                highlight.setAttribute('fill', 'rgba(66, 135, 245, 0.1)');
                highlight.setAttribute('stroke', '#4287f5'); // Blue highlight
                highlight.setAttribute('stroke-width', '2');
                highlight.setAttribute('class', 'node-highlight');
                g.appendChild(highlight);
            }

            // Simplified mouse handling to make selection work reliably
            g.addEventListener('mousedown', (e) => {
                // Record initial position for potential drag detection
                this.dragState.lastX = e.clientX;
                this.dragState.lastY = e.clientY;
            });
            
            g.addEventListener('click', (e) => {
                // Basic click detection with minimal movement tolerance
                const dx = Math.abs(e.clientX - this.dragState.lastX);
                const dy = Math.abs(e.clientY - this.dragState.lastY);

                // If minimal movement (not dragging), handle as click
                if (dx < 5 && dy < 5) {
                    // Show lines immediately on click, before selection logic
                    if (this.lineVisibilityController) {
                        this.lineVisibilityController.showLinesForNode(node.id);
                    }

                    this.handleNodeClick(e, node.id);
                }
            });

            // Node background
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            const nodeColors = this.getNodeColors(node);
            rect.setAttribute('x', node.x);
            rect.setAttribute('y', node.y);
            rect.setAttribute('width', node.width);
            rect.setAttribute('height', node.height);
            rect.setAttribute('rx', '6');
            rect.setAttribute('fill', nodeColors.fill);
            rect.setAttribute('stroke', nodeColors.stroke);
            rect.setAttribute('stroke-width', '1');
            rect.style.cursor = 'move';
            rect.addEventListener('mousedown', (e) => this.handleMouseDown(e, node.id));
            
            // Add click handler for showing node details
            rect.addEventListener('click', (e) => {
                e.stopPropagation();

                // Show lines immediately on rect click too
                if (this.lineVisibilityController) {
                    this.lineVisibilityController.showLinesForNode(node.id);
                }

                if (window.openDetailsPanel && typeof window.openDetailsPanel === 'function') {
                    window.openDetailsPanel(node);
                } else if (window.dependencyVisualizerApp?.showNodeDetails) {
                    window.dependencyVisualizerApp.showNodeDetails(node);
                }
            });

            // Node text
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', node.x + node.width / 2);
            text.setAttribute('y', node.y + node.height / 2 - 6);
            text.setAttribute('text-anchor', 'middle');
            text.setAttribute('dominant-baseline', 'middle');
            text.setAttribute('fill', '#e5e7eb');
            text.setAttribute('font-size', '10');
            text.setAttribute('font-weight', '500');
            text.style.pointerEvents = 'none';
            text.style.userSelect = 'none';
            text.textContent = node.text;

            // Filename text
            const filename = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            filename.setAttribute('x', node.x + node.width / 2);
            filename.setAttribute('y', node.y + node.height / 2 + 6);
            filename.setAttribute('text-anchor', 'middle');
            filename.setAttribute('dominant-baseline', 'middle');
            filename.setAttribute('fill', '#9ca3af');
            filename.setAttribute('font-size', '8');
            filename.setAttribute('font-weight', '400');
            filename.style.pointerEvents = 'none';
            filename.style.userSelect = 'none';
            filename.textContent = node.filename;

            g.appendChild(rect);
            g.appendChild(text);
            g.appendChild(filename);

            // Add indicators
            if (node.hasImport) {
                const importCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                importCircle.setAttribute('cx', node.x - 8);
                importCircle.setAttribute('cy', node.y + node.height / 2);
                importCircle.setAttribute('r', '4');
                importCircle.setAttribute('fill', '#ef4444');
                importCircle.setAttribute('stroke', '#dc2626');
                importCircle.setAttribute('stroke-width', '1');
                g.appendChild(importCircle);
            }

            if (node.hasExport) {
                const exportCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                exportCircle.setAttribute('cx', node.x + node.width + 8);
                exportCircle.setAttribute('cy', node.y + node.height / 2);
                exportCircle.setAttribute('r', '4');
                exportCircle.setAttribute('fill', '#22c55e');
                exportCircle.setAttribute('stroke', '#16a34a');
                exportCircle.setAttribute('stroke-width', '1');
                g.appendChild(exportCircle);
            }

            if (node.hasEvent) {
                const eventCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                eventCircle.setAttribute('cx', node.x + node.width / 2);
                eventCircle.setAttribute('cy', node.y + node.height + 8);
                eventCircle.setAttribute('r', '6'); // Increased size for better visibility
                eventCircle.setAttribute('fill', '#fbbf24'); // Brighter yellow
                eventCircle.setAttribute('stroke', '#f59e0b');
                eventCircle.setAttribute('stroke-width', '2');
                eventCircle.setAttribute('opacity', '1'); // Ensure full opacity
                eventCircle.style.pointerEvents = 'none'; // Prevent interference with interactions
                g.appendChild(eventCircle);
            }

            if (node.hasWorker) {
                const workerCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                workerCircle.setAttribute('cx', node.x + node.width / 2);
                workerCircle.setAttribute('cy', node.y - 8);
                workerCircle.setAttribute('r', '4');
                workerCircle.setAttribute('fill', '#3b82f6');
                workerCircle.setAttribute('stroke', '#2563eb');
                workerCircle.setAttribute('stroke-width', '1');
                g.appendChild(workerCircle);
            }

            parentElement.appendChild(g);
        });
    }

    renderArrows(parentElement = this.svgElement) {
        this.connections.forEach((conn) => {
            const fromNode = this.nodes.find((n) => n.id === conn.from);
            const toNode = this.nodes.find((n) => n.id === conn.to);
            
            if (!fromNode || !toNode) return;
            
            const toX = toNode.x;
            const toY = toNode.y + toNode.height / 2;

            const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrow.setAttribute(
                'points',
                `${toX - 8},${toY - 4} ${toX},${toY} ${toX - 8},${toY + 4}`, // Larger arrow
            );
            arrow.setAttribute('fill', '#9ca3af'); // Match connection color
            arrow.setAttribute('opacity', '0.9'); // Increased opacity
            this.svgElement.appendChild(arrow);
        });
    }

    /**
     * Calculate the bounds of all content for proper minimap scaling
     */
    calculateContentBounds() {
        if (this.nodes.length === 0) {
            this.contentBounds = { minX: 0, minY: 0, maxX: 1000, maxY: 550 };
            return;
        }

        let minX = Infinity;
        let minY = Infinity;
        let maxX = -Infinity;
        let maxY = -Infinity;

        // Calculate bounds from all nodes including indicators
        this.nodes.forEach(node => {
            // Account for import indicators (8px to the left)
            minX = Math.min(minX, node.hasImport ? node.x - 12 : node.x);
            // Account for worker indicators (8px above)
            minY = Math.min(minY, node.hasWorker ? node.y - 12 : node.y);
            // Account for export indicators (8px to the right)
            maxX = Math.max(maxX, node.hasExport ? node.x + node.width + 12 : node.x + node.width);
            // Account for event indicators (8px below)
            maxY = Math.max(maxY, node.hasEvent ? node.y + node.height + 12 : node.y + node.height);
        });

        // Add some padding around the content
        const padding = 50;
        this.contentBounds = {
            minX: minX - padding,
            minY: minY - padding,
            maxX: maxX + padding,
            maxY: maxY + padding
        };

        // Calculate scale and offset for minimap
        const contentWidth = this.contentBounds.maxX - this.contentBounds.minX;
        const contentHeight = this.contentBounds.maxY - this.contentBounds.minY;

        // Calculate scale to fit content in minimap
        const scaleX = this.minimapWidth / contentWidth;
        const scaleY = this.minimapHeight / contentHeight;
        this.minimapContentScale = Math.min(scaleX, scaleY) * 0.9; // 0.9 for some margin

        // Calculate offset to center content in minimap
        const scaledWidth = contentWidth * this.minimapContentScale;
        const scaledHeight = contentHeight * this.minimapContentScale;
        this.minimapOffsetX = (this.minimapWidth - scaledWidth) / 2;
        this.minimapOffsetY = (this.minimapHeight - scaledHeight) / 2;
    }

    /**
     * Convert world coordinates to minimap coordinates
     */
    worldToMinimap(worldX, worldY) {
        const relativeX = worldX - this.contentBounds.minX;
        const relativeY = worldY - this.contentBounds.minY;
        return {
            x: relativeX * this.minimapContentScale + this.minimapOffsetX,
            y: relativeY * this.minimapContentScale + this.minimapOffsetY
        };
    }

    /**
     * Convert minimap coordinates to world coordinates
     */
    minimapToWorld(minimapX, minimapY) {
        const relativeX = (minimapX - this.minimapOffsetX) / this.minimapContentScale;
        const relativeY = (minimapY - this.minimapOffsetY) / this.minimapContentScale;
        return {
            x: relativeX + this.contentBounds.minX,
            y: relativeY + this.contentBounds.minY
        };
    }

    /**
     * Clamp viewport rectangle to stay within minimap bounds
     */
    clampViewportRect(x, y, width, height) {
        // Ensure viewport rectangle stays within minimap bounds
        const clampedX = Math.max(0, Math.min(this.minimapWidth - width, x));
        const clampedY = Math.max(0, Math.min(this.minimapHeight - height, y));
        const clampedWidth = Math.min(width, this.minimapWidth);
        const clampedHeight = Math.min(height, this.minimapHeight);

        return {
            x: clampedX,
            y: clampedY,
            width: clampedWidth,
            height: clampedHeight
        };
    }

    /**
     * Create minimap container and SVG
     */
    createMinimap() {
        if (this.minimapContainer) return; // Already created

        // Find the parent container of the main SVG
        const parentContainer = this.svgElement.parentElement;
        if (!parentContainer) return;

        // Create minimap container - following exact design scheme from image
        this.minimapContainer = document.createElement('div');
        this.minimapContainer.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            width: ${this.minimapWidth}px;
            height: ${this.minimapHeight}px;
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 4px;
            overflow: hidden;
            z-index: 1000;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        `;

        // Create minimap SVG
        this.minimapSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.minimapSvg.setAttribute('width', '100%');
        this.minimapSvg.setAttribute('height', '100%');
        this.minimapSvg.setAttribute('viewBox', `0 0 ${this.minimapWidth} ${this.minimapHeight}`);
        this.minimapSvg.style.cssText = `
            width: 100%;
            height: 100%;
        `;

        // Add event listeners for minimap interaction
        this.minimapSvg.addEventListener('mousedown', this.handleMinimapMouseDown);

        this.minimapContainer.appendChild(this.minimapSvg);
        parentContainer.appendChild(this.minimapContainer);
    }

    /**
     * Render minimap content
     */
    renderMinimap() {
        if (!this.minimapSvg) return;

        // Calculate content bounds and scaling
        this.calculateContentBounds();

        // Clear minimap content
        this.minimapSvg.innerHTML = '';

        // Render minimap nodes (scaled and positioned) - following exact design scheme
        this.nodes.forEach((node) => {
            const minimapPos = this.worldToMinimap(node.x, node.y);
            const minimapSize = {
                width: node.width * this.minimapContentScale,
                height: node.height * this.minimapContentScale
            };

            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            const minimapColors = this.getNodeColors(node);
            rect.setAttribute('x', minimapPos.x);
            rect.setAttribute('y', minimapPos.y);
            rect.setAttribute('width', Math.max(3, minimapSize.width)); // Minimum size for visibility
            rect.setAttribute('height', Math.max(3, minimapSize.height));
            rect.setAttribute('rx', '1');
            rect.setAttribute('fill', '#3b82f6'); // Keep original minimap fill
            rect.setAttribute('stroke', minimapColors.stroke); // Use file type stroke
            rect.setAttribute('stroke-width', '0.5');
            rect.setAttribute('opacity', '0.8');
            this.minimapSvg.appendChild(rect);
        });

        // Render minimap connections (scaled and positioned) - subtle lines matching design
        this.connections.forEach((conn) => {
            const fromNode = this.nodes.find(n => n.id === conn.from);
            const toNode = this.nodes.find(n => n.id === conn.to);

            if (fromNode && toNode) {
                const fromPos = this.worldToMinimap(
                    fromNode.x + fromNode.width / 2,
                    fromNode.y + fromNode.height / 2
                );
                const toPos = this.worldToMinimap(
                    toNode.x + toNode.width / 2,
                    toNode.y + toNode.height / 2
                );

                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', fromPos.x);
                line.setAttribute('y1', fromPos.y);
                line.setAttribute('x2', toPos.x);
                line.setAttribute('y2', toPos.y);
                line.setAttribute('stroke', '#64748b'); // Subtle gray-blue
                line.setAttribute('stroke-width', '0.3');
                line.setAttribute('opacity', '0.4');
                this.minimapSvg.appendChild(line);
            }
        });

        // Create viewport indicator
        this.createViewportIndicator();
    }

    /**
     * Create viewport indicator on minimap
     */
    createViewportIndicator() {
        if (!this.minimapSvg) return;

        // Remove existing viewport indicator
        const existingViewport = this.minimapSvg.querySelector('.viewport-indicator');
        if (existingViewport) {
            existingViewport.remove();
        }

        // Convert viewport bounds to minimap coordinates
        const topLeft = this.worldToMinimap(this.viewBox.x, this.viewBox.y);
        const bottomRight = this.worldToMinimap(
            this.viewBox.x + this.viewBox.width,
            this.viewBox.y + this.viewBox.height
        );

        const viewportWidth = bottomRight.x - topLeft.x;
        const viewportHeight = bottomRight.y - topLeft.y;

        // Clamp viewport rectangle to minimap bounds
        const clamped = this.clampViewportRect(topLeft.x, topLeft.y, viewportWidth, viewportHeight);

        this.viewportRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        this.viewportRect.setAttribute('class', 'viewport-indicator');
        this.viewportRect.setAttribute('x', clamped.x);
        this.viewportRect.setAttribute('y', clamped.y);
        this.viewportRect.setAttribute('width', Math.max(5, clamped.width)); // Minimum size for visibility
        this.viewportRect.setAttribute('height', Math.max(5, clamped.height));
        this.viewportRect.setAttribute('fill', 'rgba(59, 130, 246, 0.2)');
        this.viewportRect.setAttribute('stroke', '#3b82f6'); // Blue stroke matching design
        this.viewportRect.setAttribute('stroke-width', '1.5');
        this.viewportRect.setAttribute('opacity', '0.9');
        this.viewportRect.setAttribute('rx', '1'); // Slight rounding to match design
        this.viewportRect.style.pointerEvents = 'none';

        this.minimapSvg.appendChild(this.viewportRect);
    }

    /**
     * Handle minimap mouse down event
     */
    handleMinimapMouseDown(e) {
        e.preventDefault();
        this.isMinimapDragging = true;
        
        // Add event listeners for minimap dragging
        document.addEventListener('mousemove', this.handleMinimapMouseMove);
        document.addEventListener('mouseup', this.handleMinimapMouseUp);
        
        // Handle initial click to center view
        this.handleMinimapClick(e);
    }

    /**
     * Handle minimap mouse move event
     */
    handleMinimapMouseMove(e) {
        if (!this.isMinimapDragging) return;
        e.preventDefault();
        this.handleMinimapClick(e);
    }

    /**
     * Handle minimap mouse up event
     */
    handleMinimapMouseUp(e) {
        this.isMinimapDragging = false;
        document.removeEventListener('mousemove', this.handleMinimapMouseMove);
        document.removeEventListener('mouseup', this.handleMinimapMouseUp);
    }

    /**
     * Handle minimap click to center view
     */
    handleMinimapClick(e) {
        if (!this.minimapSvg || !this.viewportRect) return;

        const minimapRect = this.minimapSvg.getBoundingClientRect();
        const minimapClickX = ((e.clientX - minimapRect.left) / minimapRect.width) * this.minimapWidth;
        const minimapClickY = ((e.clientY - minimapRect.top) / minimapRect.height) * this.minimapHeight;

        // Convert minimap coordinates to world coordinates
        const worldPos = this.minimapToWorld(minimapClickX, minimapClickY);

        // Update the main view to center on the clicked position
        this.viewBox.x = worldPos.x - this.viewBox.width / 2;
        this.viewBox.y = worldPos.y - this.viewBox.height / 2;

        // Apply the new viewBox
        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Update minimap to reflect current view
     */
    updateMinimap() {
        if (!this.viewportRect) return;

        // Convert viewport bounds to minimap coordinates
        const topLeft = this.worldToMinimap(this.viewBox.x, this.viewBox.y);
        const bottomRight = this.worldToMinimap(
            this.viewBox.x + this.viewBox.width,
            this.viewBox.y + this.viewBox.height
        );

        const viewportWidth = bottomRight.x - topLeft.x;
        const viewportHeight = bottomRight.y - topLeft.y;

        // Clamp viewport rectangle to minimap bounds
        const clamped = this.clampViewportRect(topLeft.x, topLeft.y, viewportWidth, viewportHeight);

        // Update viewport indicator position and size
        this.viewportRect.setAttribute('x', clamped.x);
        this.viewportRect.setAttribute('y', clamped.y);
        this.viewportRect.setAttribute('width', Math.max(5, clamped.width)); // Minimum size for visibility
        this.viewportRect.setAttribute('height', Math.max(5, clamped.height));
    }

    /**
     * Toggle minimap visibility
     */
    toggleMinimap() {
        if (!this.minimapContainer) return;

        const isVisible = this.minimapContainer.style.display !== 'none';
        this.minimapContainer.style.display = isVisible ? 'none' : 'block';
    }

    /**
     * Destroy minimap
     */
    destroyMinimap() {
        if (this.minimapContainer) {
            this.minimapContainer.remove();
            this.minimapContainer = null;
            this.minimapSvg = null;
            this.viewportRect = null;
        }
    }

    getNodes() {
        return this.nodes;
    }

    getConnections() {
        return this.connections;
    }

    getWorkerConnections() {
        return this.workerConnections;
    }

    getEventConnections() {
        return this.eventConnections;
    }

    updateNodePosition(nodeId, x, y) {
        this.nodes = this.nodes.map((node) => (node.id === nodeId ? { ...node, x, y } : node));
        this.render();
    }

    addNode(nodeData) {
        this.nodes.push(nodeData);
        this.render();
    }

    removeNode(nodeId) {
        this.nodes = this.nodes.filter((node) => node.id !== nodeId);
        this.connections = this.connections.filter(
            (conn) => conn.from !== nodeId && conn.to !== nodeId,
        );
        this.workerConnections = this.workerConnections.filter(
            (conn) => conn.from !== nodeId && conn.to !== nodeId,
        );
        this.eventConnections = this.eventConnections.filter(
            (conn) => conn.from !== nodeId && conn.to !== nodeId,
        );
        this.render();
    }

    addConnection(from, to, type = 'normal') {
        const connection = { from, to };

        if (type === 'worker') {
            connection.type = 'worker';
            this.workerConnections.push(connection);
        } else if (type === 'event') {
            connection.type = 'event';
            this.eventConnections.push(connection);
        } else {
            this.connections.push(connection);
        }
        this.render();
    }

    exportData() {
        return {
            nodes: this.nodes,
            connections: this.connections,
            workerConnections: this.workerConnections,
            eventConnections: this.eventConnections,
        };
    }

    importData(data) {
        this.nodes = data.nodes || [];
        this.connections = data.connections || [];
        this.workerConnections = data.workerConnections || [];
        this.eventConnections = data.eventConnections || [];
        this.render();
    }

    /**
     * Update mindmap with new external data
     * @param {Object} externalData - New external data
     */
    updateData(externalData) {
        this.externalData = externalData;
        this.initializeMindmap(externalData);
        this.render();
        // Ensure minimap is updated with new content bounds
        if (this.minimapSvg) {
            this.renderMinimap();
        }
    }

    /**
     * Set data model (for compatibility with dependency-mindmap.js)
     * @param {Object} dataModel - Data model with getNodes() and getLinks() methods
     */
    setDataModel(dataModel) {
        if (dataModel && typeof dataModel.getNodes === 'function' && typeof dataModel.getLinks === 'function') {
            const externalData = {
                nodes: dataModel.getNodes(),
                links: dataModel.getLinks()
            };
            this.updateData(externalData);

            // Force a render to ensure connections are drawn
            this.render();

            // Hide all lines after data update (they will show when nodes are selected)
            setTimeout(() => {
                if (this.lineVisibilityController) {
                    this.lineVisibilityController.hideAllLinesForNodeSelection();
                }
            }, 100);
        }
    }
    
    /**
     * Initialize the line visibility controller
     */
    initializeLineVisibilityController() {
        if (typeof window.LineVisibilityController === 'undefined') {
            console.warn('LineVisibilityController not available');
            return;
        }
        
        try {
            this.lineVisibilityController = new window.LineVisibilityController(this);
        } catch (error) {
            console.error('Failed to initialize line visibility controller:', error);
        }
    }
    
    /**
     * Get the line visibility controller
     */
    getLineVisibilityController() {
        return this.lineVisibilityController;
    }
    
    /**
     * Toggle line visibility panel
     */
    toggleLineVisibilityPanel() {
        if (this.lineVisibilityController) {
            this.lineVisibilityController.toggleControlPanel();
        }
    }

    destroy() {
        this.removeEventListeners();
        this.destroyMinimap();
        
        // Destroy line visibility controller
        if (this.lineVisibilityController) {
            this.lineVisibilityController.destroy();
            this.lineVisibilityController = null;
        }
        
        this.svgElement = null;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MindmapProject;
} else if (typeof window !== 'undefined') {
    window.MindmapProject = MindmapProject;
}
