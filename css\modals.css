/* Modal styles */
.modal {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
}

/* Modal box */
.modal-content {
    position: relative;
    margin: 5% auto;
    padding: 0;
    background-color: var(--google-surface);
    border-radius: var(--radius-md);
    width: 80%;
    max-width: 900px;
    max-height: 80vh;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Modal header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--google-panel-header);
    border-bottom: 1px solid var(--google-divider);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--google-on-surface);
}

/* Close button */
.close {
    font-size: 24px;
    font-weight: bold;
    color: var(--google-on-surface-disabled);
    cursor: pointer;
    border: none;
    background: none;
}

.close:hover {
    color: var(--google-on-surface);
}

/* Modal body for code */
.modal-body {
    padding: var(--spacing-md);
    overflow: auto;
    background-color: var(--google-surface);
    flex: 1;
}

/* Code preview styling */
.code-preview-pre {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
    color: var(--google-on-surface);
    white-space: pre;
    line-height: 1.4;
}

/* Highlighted line */
.highlighted-line {
    background-color: var(--google-active-overlay);
}

/* Line number style */
.line-number {
    display: inline-block;
    width: 3em;
    text-align: right;
    margin-right: var(--spacing-sm);
    color: var(--google-on-surface-medium);
}

.code-preview-content {
    background-color: var(--google-surface-variant, #1e1e1e);
    border-radius: 6px;
    padding: 15px;
    max-height: 60vh; /* Max height before scrolling */
    overflow-y: auto; /* Scroll for long code */
    border: 1px solid var(--google-divider, #383838);
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.code-preview-content::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.code-preview-content::-webkit-scrollbar-thumb {
    background-color: #555;
    border-radius: 4px;
    border: 2px solid #2a2a2a;
}

/* Code actions styling */
.code-actions {
    margin-top: 10px;
    text-align: right;
    padding-top: 10px;
    border-top: 1px solid var(--google-divider, #383838);
}

.copy-btn {
    padding: 6px 12px;
    background-color: var(--google-primary, #007bff);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: background-color 0.2s ease;
}

.copy-btn:hover {
    background-color: var(--google-primary-dark, #0056b3);
}

/* Error message styling */
.error-message {
    color: var(--google-error, #dc3545);
    padding: 15px;
    border: 1px solid var(--google-error, #dc3545);
    background-color: var(--google-error-container, rgba(220,53,69,0.1));
    border-radius: 4px;
    text-align: center;
}
