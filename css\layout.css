/* Layout styles for responsive design */

/* Main app container */
.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

/* Sidebar styles */
.sidebar {
    width: 260px;
    min-width: 260px;
    background-color: var(--google-surface);
    border-right: 1px solid var(--google-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 100;
}

.sidebar-header {
    padding: var(--spacing-md);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--google-on-surface);
    border-bottom: 1px solid var(--google-border);
    background-color: var(--google-panel-header);
}

.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-sm) 0;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: background-color 0.2s;
    color: var(--google-on-surface-medium);
}

.sidebar-item:hover {
    background-color: var(--google-hover-overlay);
}

.sidebar-item.active {
    background-color: var(--google-primary-container);
    color: var(--google-on-primary-container);
}

.sidebar-item-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-item-icon svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.sidebar-footer-text {
    padding: var(--spacing-md);
    border-top: 1px solid var(--google-border);
    font-size: var(--font-size-sm);
    color: var(--google-on-surface-medium);
    line-height: 1.4;
}

/* Main content area */
.app-container > .app-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* Content scroll area */
.content-scroll-area {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    transition: margin-right 0.3s ease, margin-left 0.3s ease;
    margin-right: 0;
    margin-left: 0; /* Default: no left margin */
}

.content-scroll-area.panel-open {
    margin-right: 400px;
}

.content-scroll-area.scan-summary-active {
    margin-left: 350px; /* When scan summary panel is active */
}

.content-scroll-area.scan-summary-collapsed {
    margin-left: 40px; /* When scan summary is collapsed */
}

/* Card styles */
.card {
    background-color: var(--google-surface-variant);
    border: 1px solid var(--google-border);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--elevation-1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--google-panel-header);
    border-bottom: 1px solid var(--google-border);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--google-on-surface);
    margin: 0;
}

.card-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.card-content {
    padding: var(--spacing-md);
    flex: 1;
    overflow: hidden;
}

/* Project selection card */
#project-selection-card {
    flex-shrink: 0;
}

/* Visualization card */
#visualization-card {
    flex: 1;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

#visualization-card .card-content {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
    position: relative;
}

#visualization-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: var(--google-background);
}

/* Actions card */
#actions-card {
    flex-shrink: 0;
}

/* Progress bar styles */
#progress-bar-container {
    width: 100%;
    height: 4px;
    background-color: var(--google-surface);
    border-radius: 2px;
    overflow: hidden;
    margin-top: var(--spacing-sm);
    display: none;
}

#progress-bar {
    height: 100%;
    background-color: var(--google-primary);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    color: var(--google-on-primary);
    font-weight: var(--font-weight-medium);
}

/* Status text */
#status {
    font-size: var(--font-size-sm);
    color: var(--google-on-surface-medium);
    margin-bottom: var(--spacing-xs);
}

/* Responsive design */
@media (max-width: 1200px) {
    .content-scroll-area.scan-summary-active {
        margin-left: 300px; /* Smaller scan summary panel */
    }

    .content-scroll-area.panel-open {
        margin-right: 350px;
    }

    .content-scroll-area.scan-summary-collapsed {
        margin-left: 40px;
    }

    #scan-summary-panel {
        width: 300px;
    }

    #details-panel-container {
        width: 350px;
        right: -350px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 200px;
        min-width: 200px;
    }
    
    .sidebar-header {
        padding: var(--spacing-sm);
        font-size: var(--font-size-md);
    }
    
    .sidebar-item {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .content-scroll-area {
        padding: var(--spacing-sm);
    }

    .content-scroll-area.scan-summary-active {
        margin-left: 250px; /* Smaller scan summary panel */
    }

    .content-scroll-area.scan-summary-collapsed {
        margin-left: 40px;
    }

    .content-scroll-area.panel-open {
        margin-right: 300px;
    }

    #scan-summary-panel {
        left: 200px; /* After smaller sidebar */
        width: 250px;
    }

    #details-panel-container {
        width: 300px;
        right: -300px;
    }
    
    .card-header {
        padding: var(--spacing-sm);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .card-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    .card-content {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        min-width: auto;
        height: auto;
        max-height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--google-border);
    }

    .sidebar-menu {
        display: flex;
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
        padding: var(--spacing-xs);
    }

    .sidebar-item {
        white-space: nowrap;
        min-width: auto;
    }

    .content-scroll-area {
        margin-left: 0; /* No left margin on mobile */
        margin-top: 200px; /* Account for scan summary at top */
    }

    .content-scroll-area.panel-open {
        margin-right: 0;
        margin-bottom: 250px;
    }

    .content-scroll-area.scan-summary-collapsed {
        margin-top: 50px; /* Smaller margin when collapsed */
    }

    #scan-summary-panel {
        position: fixed;
        top: 200px; /* Below sidebar */
        left: 0;
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    }

    #scan-summary-panel.collapsed {
        height: 50px;
        left: 0;
        width: 100%;
    }

    #details-panel-container {
        top: auto;
        bottom: -250px;
        right: 0;
        width: 100%;
        height: 250px;
        transition: bottom 0.3s ease;
    }

    #details-panel-container.open {
        bottom: 0;
    }
}

/* Ensure proper scrolling */
.content-scroll-area::-webkit-scrollbar {
    width: 8px;
}

.content-scroll-area::-webkit-scrollbar-track {
    background: var(--google-surface);
}

.content-scroll-area::-webkit-scrollbar-thumb {
    background: var(--google-border);
    border-radius: 4px;
}

.content-scroll-area::-webkit-scrollbar-thumb:hover {
    background: var(--google-on-surface-medium);
}

/* Fix for visualization container */
#visualization-container {
    width: 100%;
    height: 100%;
    min-height: 400px;
}

/* Ensure cards don't overflow */
.card {
    max-width: 100%;
    overflow: hidden;
}

/* File input styling improvements */
input[type="file"] {
    width: 100%;
    max-width: 100%;
}

/* Button container responsive */
#process-buttons-container {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

@media (max-width: 600px) {
    #process-buttons-container {
        flex-direction: column;
        width: 100%;
    }
    
    #process-buttons-container button {
        width: 100%;
    }
}