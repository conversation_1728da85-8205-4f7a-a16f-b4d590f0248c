/**
 * Safe Regex Processor
 * Provides timeout-protected regex processing to prevent hangs on large files
 */

class SafeRegexProcessor {
    constructor(options = {}) {
        this.defaultTimeout = options.defaultTimeout || 5000; // 5 seconds
        this.maxContentLength = options.maxContentLength || 1024 * 1024; // 1MB
        this.chunkSize = options.chunkSize || 50000; // 50KB chunks
        this.fallbackEnabled = options.fallbackEnabled !== false;
        
        // Statistics
        this.stats = {
            processedFiles: 0,
            timeouts: 0,
            fallbacks: 0,
            totalProcessingTime: 0,
            averageProcessingTime: 0
        };
        
        // Bind methods
        this.processWithTimeout = this.processWithTimeout.bind(this);
        this.processInChunks = this.processInChunks.bind(this);
        this.fallbackParsing = this.fallbackParsing.bind(this);
    }

    /**
     * Process content with timeout protection
     * @param {string} content - Content to process
     * @param {Object} patterns - Regex patterns to apply
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} - Processing results
     */
    async processWithTimeout(content, patterns, options = {}) {
        const startTime = Date.now();
        const timeout = options.timeout || this.defaultTimeout;
        const filePath = options.filePath || 'unknown';
        
        try {
            // Validate input
            if (!content || typeof content !== 'string') {
                return this.createEmptyResult();
            }
            
            // Check content length
            if (content.length > this.maxContentLength) {
                console.warn(`Large file detected (${content.length} chars): ${filePath}`);
                return await this.processLargeContent(content, patterns, options);
            }
            
            // Process with timeout
            const result = await Promise.race([
                this.processContent(content, patterns, options),
                this.createTimeoutPromise(timeout, filePath)
            ]);
            
            // Update statistics
            const processingTime = Date.now() - startTime;
            this.updateStats(processingTime, false, false);
            
            return result;
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            
            if (error.name === 'TimeoutError') {
                console.warn(`Regex processing timeout for ${filePath} after ${timeout}ms`);
                this.updateStats(processingTime, true, false);
                
                if (this.fallbackEnabled) {
                    return await this.fallbackParsing(content, patterns, options);
                } else {
                    return this.createEmptyResult();
                }
            } else {
                console.error(`Error processing ${filePath}:`, error);
                this.updateStats(processingTime, false, false);
                
                if (this.fallbackEnabled) {
                    return await this.fallbackParsing(content, patterns, options);
                } else {
                    throw error;
                }
            }
        }
    }

    /**
     * Process large content in chunks
     * @param {string} content - Large content to process
     * @param {Object} patterns - Regex patterns
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} - Combined results
     */
    async processLargeContent(content, patterns, options = {}) {
        const chunks = this.splitIntoChunks(content, this.chunkSize);
        const results = [];
        
        for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i];
            const chunkOptions = {
                ...options,
                chunkIndex: i,
                totalChunks: chunks.length,
                isChunk: true
            };
            
            try {
                const chunkResult = await this.processWithTimeout(chunk.content, patterns, chunkOptions);
                
                // Adjust line numbers for chunk offset
                this.adjustLineNumbers(chunkResult, chunk.startLine);
                results.push(chunkResult);
                
                // Yield control between chunks
                await this.yieldControl();
                
            } catch (error) {
                console.warn(`Error processing chunk ${i} of ${chunks.length}:`, error);
                results.push(this.createEmptyResult());
            }
        }
        
        return this.combineResults(results);
    }

    /**
     * Split content into manageable chunks
     * @param {string} content - Content to split
     * @param {number} chunkSize - Size of each chunk
     * @returns {Array} - Array of chunks with metadata
     */
    splitIntoChunks(content, chunkSize) {
        const chunks = [];
        const lines = content.split('\n');
        let currentChunk = '';
        let currentStartLine = 1;
        let currentLineCount = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            if (currentChunk.length + line.length > chunkSize && currentChunk.length > 0) {
                // Save current chunk
                chunks.push({
                    content: currentChunk,
                    startLine: currentStartLine,
                    endLine: currentStartLine + currentLineCount - 1,
                    lineCount: currentLineCount
                });
                
                // Start new chunk
                currentChunk = line + '\n';
                currentStartLine = i + 1;
                currentLineCount = 1;
            } else {
                currentChunk += line + '\n';
                currentLineCount++;
            }
        }
        
        // Add final chunk
        if (currentChunk.length > 0) {
            chunks.push({
                content: currentChunk,
                startLine: currentStartLine,
                endLine: currentStartLine + currentLineCount - 1,
                lineCount: currentLineCount
            });
        }
        
        return chunks;
    }

    /**
     * Process content with regex patterns
     * @param {string} content - Content to process
     * @param {Object} patterns - Regex patterns
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} - Processing results
     */
    async processContent(content, patterns, options = {}) {
        const result = this.createEmptyResult();
        
        // Process each pattern type
        for (const [patternName, pattern] of Object.entries(patterns)) {
            try {
                const matches = await this.applyPattern(content, pattern, patternName, options);
                this.addMatchesToResult(result, patternName, matches);
                
                // Yield control periodically
                if (Object.keys(patterns).length > 10) {
                    await this.yieldControl();
                }
                
            } catch (error) {
                console.warn(`Error applying pattern ${patternName}:`, error);
                // Continue with other patterns
            }
        }
        
        return result;
    }

    /**
     * Apply a single regex pattern with safety checks
     * @param {string} content - Content to search
     * @param {RegExp} pattern - Regex pattern
     * @param {string} patternName - Pattern name for debugging
     * @param {Object} options - Processing options
     * @returns {Promise<Array>} - Array of matches
     */
    async applyPattern(content, pattern, patternName, options = {}) {
        const matches = [];
        const maxMatches = options.maxMatches || 1000;
        let matchCount = 0;
        let match;
        
        // Reset pattern lastIndex to ensure clean state
        if (pattern.global) {
            pattern.lastIndex = 0;
        }
        
        // Apply pattern with match limit
        while ((match = pattern.exec(content)) !== null && matchCount < maxMatches) {
            matches.push({
                match: match[0],
                groups: Array.from(match).slice(1),
                index: match.index,
                line: this.getLineNumber(content, match.index)
            });
            
            matchCount++;
            
            // Prevent infinite loops with non-global patterns
            if (!pattern.global) {
                break;
            }
            
            // Yield control every 100 matches
            if (matchCount % 100 === 0) {
                await this.yieldControl();
            }
        }
        
        // Reset pattern lastIndex
        if (pattern.global) {
            pattern.lastIndex = 0;
        }
        
        if (matchCount >= maxMatches) {
            console.warn(`Pattern ${patternName} hit match limit (${maxMatches}) - results may be incomplete`);
        }
        
        return matches;
    }

    /**
     * Fallback parsing using simpler methods
     * @param {string} content - Content to parse
     * @param {Object} patterns - Original patterns (for reference)
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} - Fallback results
     */
    async fallbackParsing(content, patterns, options = {}) {
        console.log('Using fallback parsing for', options.filePath || 'unknown file');
        this.updateStats(0, false, true);
        
        const result = this.createEmptyResult();
        
        try {
            // Simple string-based parsing for common patterns
            const lines = content.split('\n');
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                const lineNumber = i + 1;
                
                // Simple import detection
                if (line.includes('import ') && (line.includes(' from ') || line.includes('import('))) {
                    this.addSimpleMatch(result, 'imports', line, lineNumber);
                }
                
                // Simple require detection
                if (line.includes('require(') && (line.includes('const ') || line.includes('let ') || line.includes('var '))) {
                    this.addSimpleMatch(result, 'imports', line, lineNumber);
                }
                
                // Simple export detection
                if (line.startsWith('export ')) {
                    this.addSimpleMatch(result, 'exports', line, lineNumber);
                }
                
                // Simple function detection
                if (line.includes('function ') || line.includes(') => ')) {
                    this.addSimpleMatch(result, 'functions', line, lineNumber);
                }
                
                // Simple class detection
                if (line.includes('class ')) {
                    this.addSimpleMatch(result, 'classes', line, lineNumber);
                }
                
                // Yield control every 1000 lines
                if (i % 1000 === 0) {
                    await this.yieldControl();
                }
            }
            
        } catch (error) {
            console.error('Fallback parsing failed:', error);
        }
        
        return result;
    }

    /**
     * Add simple match to result
     * @param {Object} result - Result object
     * @param {string} type - Match type
     * @param {string} line - Line content
     * @param {number} lineNumber - Line number
     */
    addSimpleMatch(result, type, line, lineNumber) {
        if (!result[type]) {
            result[type] = [];
        }
        
        result[type].push({
            match: line.trim(),
            line: lineNumber,
            simplified: true
        });
    }

    /**
     * Create timeout promise
     * @param {number} timeout - Timeout in milliseconds
     * @param {string} filePath - File path for error message
     * @returns {Promise} - Promise that rejects on timeout
     */
    createTimeoutPromise(timeout, filePath) {
        return new Promise((_, reject) => {
            setTimeout(() => {
                const error = new Error(`Regex processing timeout: ${filePath}`);
                error.name = 'TimeoutError';
                reject(error);
            }, timeout);
        });
    }

    /**
     * Create empty result structure
     * @returns {Object} - Empty result
     */
    createEmptyResult() {
        return {
            imports: [],
            exports: [],
            functions: [],
            classes: [],
            variables: [],
            events: []
        };
    }

    /**
     * Add matches to result object
     * @param {Object} result - Result object
     * @param {string} patternName - Pattern name
     * @param {Array} matches - Array of matches
     */
    addMatchesToResult(result, patternName, matches) {
        // Map pattern names to result categories
        const categoryMap = {
            // Import patterns
            jsImport: 'imports',
            jsBasicImport: 'imports',
            jsRequire: 'imports',
            jsRequireSimple: 'imports',
            dynamicImport: 'imports',
            tsImportType: 'imports',

            // Export patterns
            jsExport: 'exports',
            jsExportFrom: 'exports',
            jsExportAll: 'exports',
            jsExportDestructured: 'exports',
            jsExportConstVar: 'exports',
            es6DefaultExport: 'exports',
            es6NamedExport: 'exports',
            moduleExports: 'exports',
            exportsProperty: 'exports',
            tsExportType: 'exports',

            // Function patterns
            function: 'functions',
            arrowFunction: 'functions',

            // Class patterns
            class: 'classes',
            tsInterface: 'classes',

            // Event patterns
            eventListener: 'events',
            eventDispatcher: 'events',
            eventEmitter: 'events',

            // Worker patterns
            workerCreation: 'workers',
            workerImport: 'workers',
            workerVariable: 'workers',
            dynamicWorkerCreation: 'workers',
            sharedWorker: 'workers',
            serviceWorker: 'workers',

            // Variable patterns
            varDeclaration: 'variables',
            varAssignment: 'variables',
            varWithValue: 'variables',
            destructuringVar: 'variables',
            arrayDestructuring: 'variables',
            objectProperty: 'variables'
        };
        
        const category = categoryMap[patternName] || 'other';
        
        if (!result[category]) {
            result[category] = [];
        }
        
        result[category].push(...matches);
    }

    /**
     * Adjust line numbers for chunk processing
     * @param {Object} result - Result object
     * @param {number} offset - Line number offset
     */
    adjustLineNumbers(result, offset) {
        for (const category of Object.values(result)) {
            if (Array.isArray(category)) {
                category.forEach(item => {
                    if (item.line) {
                        item.line += offset - 1;
                    }
                });
            }
        }
    }

    /**
     * Combine results from multiple chunks
     * @param {Array} results - Array of result objects
     * @returns {Object} - Combined result
     */
    combineResults(results) {
        const combined = this.createEmptyResult();
        
        for (const result of results) {
            for (const [category, items] of Object.entries(result)) {
                if (Array.isArray(items)) {
                    combined[category].push(...items);
                }
            }
        }
        
        return combined;
    }

    /**
     * Get line number from character index
     * @param {string} content - Content string
     * @param {number} index - Character index
     * @returns {number} - Line number
     */
    getLineNumber(content, index) {
        return content.substring(0, index).split('\n').length;
    }

    /**
     * Update processing statistics
     * @param {number} processingTime - Processing time in ms
     * @param {boolean} wasTimeout - Whether operation timed out
     * @param {boolean} wasFallback - Whether fallback was used
     */
    updateStats(processingTime, wasTimeout, wasFallback) {
        this.stats.processedFiles++;
        this.stats.totalProcessingTime += processingTime;
        this.stats.averageProcessingTime = this.stats.totalProcessingTime / this.stats.processedFiles;
        
        if (wasTimeout) {
            this.stats.timeouts++;
        }
        
        if (wasFallback) {
            this.stats.fallbacks++;
        }
    }

    /**
     * Yield control to the event loop
     * @param {number} delay - Delay in milliseconds
     */
    async yieldControl(delay = 0) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    /**
     * Get processing statistics
     * @returns {Object} - Processing statistics
     */
    getStats() {
        return {
            ...this.stats,
            timeoutRate: this.stats.processedFiles > 0 ? this.stats.timeouts / this.stats.processedFiles : 0,
            fallbackRate: this.stats.processedFiles > 0 ? this.stats.fallbacks / this.stats.processedFiles : 0
        };
    }

    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            processedFiles: 0,
            timeouts: 0,
            fallbacks: 0,
            totalProcessingTime: 0,
            averageProcessingTime: 0
        };
    }
}

export default SafeRegexProcessor;